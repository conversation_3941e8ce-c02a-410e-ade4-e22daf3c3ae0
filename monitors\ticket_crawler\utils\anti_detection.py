"""
反爬蟲檢測工具
提供各種反爬蟲檢測的對策
"""

import random
import asyncio
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger('monitors.ticket_crawler.anti_detection')

@dataclass
class UserAgentConfig:
    """User-Agent 配置"""
    desktop_chrome: List[str]
    desktop_firefox: List[str]
    mobile_chrome: List[str]
    mobile_safari: List[str]

class AntiDetectionMixin:
    """
    反爬蟲檢測混入類
    提供各種反爬蟲檢測的對策方法
    """
    
    def __init__(self):
        self.user_agents = UserAgentConfig(
            desktop_chrome=[
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            ],
            desktop_firefox=[
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0",
                "Mozilla/5.0 (X11; Linux x86_64; rv:121.0) Gecko/20100101 Firefox/121.0",
            ],
            mobile_chrome=[
                "Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/120.0.6099.119 Mobile/15E148 Safari/604.1",
                "Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.6099.193 Mobile Safari/537.36",
            ],
            mobile_safari=[
                "Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1",
                "Mozilla/5.0 (iPad; CPU OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1",
            ]
        )
        
        self.current_user_agent = None
        self.request_count = 0
        
    def get_random_user_agent(self, device_type: str = "desktop") -> str:
        """
        獲取隨機 User-Agent
        
        Args:
            device_type: 設備類型 ("desktop", "mobile")
            
        Returns:
            str: 隨機 User-Agent
        """
        if device_type == "desktop":
            all_agents = self.user_agents.desktop_chrome + self.user_agents.desktop_firefox
        elif device_type == "mobile":
            all_agents = self.user_agents.mobile_chrome + self.user_agents.mobile_safari
        else:
            all_agents = (self.user_agents.desktop_chrome + 
                         self.user_agents.desktop_firefox + 
                         self.user_agents.mobile_chrome + 
                         self.user_agents.mobile_safari)
        
        return random.choice(all_agents)
    
    def rotate_user_agent(self, force: bool = False) -> str:
        """
        輪換 User-Agent
        
        Args:
            force: 是否強制輪換
            
        Returns:
            str: 新的 User-Agent
        """
        # 每10個請求或強制時輪換
        if force or self.request_count % 10 == 0:
            self.current_user_agent = self.get_random_user_agent()
            logger.debug(f"Rotated User-Agent: {self.current_user_agent[:50]}...")
        
        return self.current_user_agent or self.get_random_user_agent()
    
    async def random_delay(self, min_seconds: float = 1.0, max_seconds: float = 3.0):
        """
        隨機延遲
        
        Args:
            min_seconds: 最小延遲秒數
            max_seconds: 最大延遲秒數
        """
        delay = random.uniform(min_seconds, max_seconds)
        logger.debug(f"Random delay: {delay:.2f} seconds")
        await asyncio.sleep(delay)
    
    def get_random_headers(self, base_headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """
        獲取隨機化的請求頭
        
        Args:
            base_headers: 基礎請求頭
            
        Returns:
            Dict[str, str]: 隨機化的請求頭
        """
        headers = base_headers.copy() if base_headers else {}
        
        # 設置 User-Agent
        headers["User-Agent"] = self.rotate_user_agent()
        
        # 隨機化其他頭部
        headers.update({
            "Accept": random.choice([
                "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
                "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            ]),
            "Accept-Language": random.choice([
                "zh-TW,zh;q=0.9,en;q=0.8",
                "zh-CN,zh;q=0.9,en;q=0.8",
                "en-US,en;q=0.9",
                "zh-TW,zh;q=0.8,en-US;q=0.5,en;q=0.3",
            ]),
            "Accept-Encoding": "gzip, deflate, br",
            "DNT": random.choice(["1", "0"]),
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
        })
        
        # 隨機添加一些可選頭部
        optional_headers = {
            "Cache-Control": random.choice(["no-cache", "max-age=0"]),
            "Pragma": "no-cache",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": random.choice(["none", "same-origin", "cross-site"]),
        }
        
        # 隨機選擇是否添加可選頭部
        for key, value in optional_headers.items():
            if random.random() > 0.3:  # 70% 機率添加
                headers[key] = value
        
        self.request_count += 1
        return headers
    
    def simulate_human_behavior(self) -> Dict[str, Any]:
        """
        模擬人類行為參數
        
        Returns:
            Dict[str, Any]: 行為參數
        """
        return {
            "scroll_pause_time": random.uniform(0.5, 2.0),
            "click_delay": random.uniform(0.1, 0.5),
            "typing_delay": random.uniform(0.05, 0.2),
            "page_load_wait": random.uniform(2.0, 5.0),
            "mouse_move_duration": random.uniform(0.5, 1.5),
        }
    
    def get_proxy_config(self, proxy_list: Optional[List[str]] = None) -> Optional[str]:
        """
        獲取代理配置
        
        Args:
            proxy_list: 代理列表
            
        Returns:
            str: 隨機選擇的代理，如果沒有則返回None
        """
        if not proxy_list:
            return None
        
        return random.choice(proxy_list)
    
    def should_use_proxy(self, failure_rate: float = 0.1) -> bool:
        """
        判斷是否應該使用代理
        
        Args:
            failure_rate: 失敗率閾值
            
        Returns:
            bool: 是否使用代理
        """
        # 根據請求失敗率決定是否使用代理
        # 這裡可以根據實際情況調整邏輯
        return random.random() < failure_rate
    
    def get_selenium_options(self, headless: bool = True) -> Dict[str, Any]:
        """
        獲取 Selenium 瀏覽器選項
        
        Args:
            headless: 是否無頭模式
            
        Returns:
            Dict[str, Any]: 瀏覽器選項
        """
        options = {
            "headless": headless,
            "disable_gpu": True,
            "no_sandbox": True,
            "disable_dev_shm_usage": True,
            "disable_extensions": True,
            "disable_plugins": True,
            "disable_images": random.choice([True, False]),  # 隨機是否載入圖片
            "window_size": random.choice([
                (1920, 1080),
                (1366, 768), 
                (1440, 900),
                (1536, 864),
            ]),
            "user_agent": self.get_random_user_agent(),
        }
        
        # 隨機添加一些額外選項
        extra_options = [
            "--disable-blink-features=AutomationControlled",
            "--disable-web-security",
            "--allow-running-insecure-content",
            "--disable-features=TranslateUI",
            "--disable-ipc-flooding-protection",
        ]
        
        options["extra_options"] = random.sample(extra_options, k=random.randint(2, len(extra_options)))
        
        return options
