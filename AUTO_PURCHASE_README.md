# 自動搶票功能使用說明

本文件說明如何使用 TicketPlus 自動搶票功能。

## 功能概述

自動搶票功能可以：
1. 監控指定活動的票券可用性
2. 當票券可用時，自動登入並購買票券
3. 支持指定區域和票數
4. 處理驗證碼和其他安全措施

## 安裝依賴

自動搶票功能需要安裝以下依賴：

```bash
pip install selenium webdriver-manager
```

或者使用 requirements.txt 安裝所有依賴：

```bash
pip install -r requirements.txt
```

## 配置

在使用自動搶票功能前，需要在 `ticket_alert_config.json` 文件中設置以下配置：

```json
{
    "ticketplus_username": "<EMAIL>",
    "ticketplus_password": "your_password",
    "auto_purchase": {
        "enabled": true,
        "target_areas": ["搖滾區", "看台區"],
        "ticket_count": 2,
        "auto_confirm": false,
        "buyer_info": {
            "name": "張三",
            "phone": "0912345678",
            "email": "<EMAIL>"
        }
    }
}
```

- `ticketplus_username`：TicketPlus 帳號（電子郵件）
- `ticketplus_password`：TicketPlus 密碼
- `auto_purchase.enabled`：是否啟用自動搶票功能
- `auto_purchase.target_areas`：目標區域列表，按優先順序排序
- `auto_purchase.ticket_count`：票數
- `auto_purchase.auto_confirm`：是否自動確認購買（謹慎使用）
- `auto_purchase.buyer_info`：購票人信息（如果需要填寫）

## 使用方法

### 方法 1：使用 Discord 斜線指令

如果您已經設置了 Discord 機器人，可以使用以下斜線指令：

```
/auto_purchase [event_index] [areas] [count] [auto_confirm]
```

參數說明：
- `event_index`：活動索引，如果不指定則使用第一個活動（默認：0）
- `areas`：目標區域，多個區域用逗號分隔，按優先順序排序（默認：使用配置文件中的設置）
- `count`：票數（默認：2）
- `auto_confirm`：是否自動確認購買（默認：false）

### 方法 2：使用命令行工具

您也可以使用命令行工具 `auto_purchase_cli.py` 來執行自動搶票：

```bash
python auto_purchase_cli.py --url "https://ticketplus.com.tw/order/xxx" --areas "搖滾區" "看台區" --count 2
```

參數說明：
- `--url`：活動 URL（如果不指定則使用配置文件中的第一個 URL）
- `--username`：TicketPlus 帳號（如果不指定則使用配置文件中的設置）
- `--password`：TicketPlus 密碼（如果不指定則使用配置文件中的設置）
- `--areas`：目標區域列表，按優先順序排序（如果不指定則使用配置文件中的設置）
- `--count`：票數（默認：2）
- `--headless`：是否使用無頭模式（不顯示瀏覽器界面）
- `--auto-confirm`：是否自動確認購買（謹慎使用）
- `--save-config`：是否保存配置到配置文件

## 注意事項

1. **法律風險**：自動搶票可能違反網站的服務條款，請謹慎使用。
2. **帳戶風險**：帳戶可能被封鎖，請謹慎使用。
3. **付款風險**：如果啟用自動確認購買，可能會自動完成付款，請謹慎使用。
4. **驗證碼**：如果遇到驗證碼，程序會暫停等待用戶手動輸入。
5. **瀏覽器**：程序會打開一個瀏覽器窗口，請不要關閉它。
6. **網絡**：請確保網絡連接穩定，否則可能會影響搶票成功率。
7. **硬體**：請使用性能較好的電腦，否則可能會影響搶票成功率。

## 常見問題

### Q: 為什麼我看不到瀏覽器窗口？
A: 可能是因為您啟用了無頭模式（`--headless`）。請移除此參數，或將其設置為 `false`。

### Q: 為什麼程序一直在等待？
A: 可能是因為遇到了驗證碼或其他需要用戶手動操作的步驟。請檢查瀏覽器窗口。

### Q: 為什麼我的帳戶被封鎖了？
A: 可能是因為您使用了自動搶票功能，違反了網站的服務條款。請謹慎使用此功能。

### Q: 如何提高搶票成功率？
A: 使用性能較好的電腦、穩定的網絡連接，並且提前設置好所有配置。

## 免責聲明

本功能僅供學習和研究使用，請勿用於商業用途。使用本功能可能違反網站的服務條款，請謹慎使用。作者不對使用本功能造成的任何損失負責。
