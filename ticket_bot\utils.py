import discord
import datetime
from zoneinfo import ZoneInfo
from typing import Dict

# Global timezone object for reuse
taiwan_tz = ZoneInfo("Asia/Taipei")

# Cache for embed colors to avoid recalculation
color_cache = {
    0: discord.Color.from_rgb(255, 0, 0),    # Red
    1: discord.Color.from_rgb(255, 165, 0),  # Orange
    2: discord.Color.from_rgb(255, 215, 0),  # Gold
    3: discord.Color.from_rgb(0, 255, 0)     # Green
}

def get_embed_color(ticket_counts: Dict[str, int]) -> discord.Color:
    """
    Determine embed color based on ticket availability
    """
    total_tickets = sum(ticket_counts.values())
    
    # Use cached colors for better performance
    if total_tickets == 0:
        return color_cache[0]  # Red
    elif total_tickets < 10:
        return color_cache[1]  # Orange
    elif total_tickets < 50:
        return color_cache[2]  # Gold
    else:
        return color_cache[3]  # Green

def format_message(session_info: Dict, ticket_data: Dict[str, int]) -> discord.Embed:
    """Format Discord message using rich embed with dynamic colors"""
    # Get current time in Taiwan timezone
    taiwan_now = datetime.datetime.now(taiwan_tz)
    formatted_time = taiwan_now.strftime('%Y/%m/%d %H:%M:%S')
    
    # Get color based on ticket availability
    embed_color = get_embed_color(ticket_data)
    total_tickets = sum(ticket_data.values())
    
    # Create embed object
    embed = discord.Embed(
        title=f"🎟️ 票券通知: {session_info['title']}",
        url=session_info['url'],
        color=embed_color,
        timestamp=taiwan_now  # Use the same timestamp object
    )
    
    # Add fields
    embed.add_field(
        name="🎫 演出時間",
        value=f"{session_info['date']} {session_info['time']}",
        inline=False
    )
    
    embed.add_field(
        name="⏰ 通知時間",
        value=f"{formatted_time} (Asia/Taipei)",
        inline=False
    )
    
    # Add ticket information - use a list comprehension for better performance
    ticket_info_parts = []
    for area, count in ticket_data.items():
        # Add emoji indicators based on count
        if count == 0:
            indicator = "❌"
        elif count < 10:
            indicator = "⚠️"
        else:
            indicator = "✅"
        ticket_info_parts.append(f"{indicator} **{area}**: {count}")
    
    ticket_info = "\n".join(ticket_info_parts)
    
    embed.add_field(
        name=f"🎟️ 票務狀態 (總計: {total_tickets})",
        value=ticket_info or "No tickets available",
        inline=False
    )
    
    # Set thumbnail if picture URL is available
    if session_info.get('pic'):
        embed.set_thumbnail(url=session_info['pic'])
    
    return embed
