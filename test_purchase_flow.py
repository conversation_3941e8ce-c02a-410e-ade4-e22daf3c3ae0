#!/usr/bin/env python3
"""
測試購票流程腳本
"""
import os
import sys
import json
import argparse
import logging
from ticket_bot.auto_purchase import TicketPlusAutoPurchase

# 配置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("test_purchase_flow.log", encoding="utf-8"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('test_purchase_flow')

def load_config():
    """加載配置文件"""
    config_path = os.path.join(os.path.dirname(__file__), "ticket_alert_config.json")
    try:
        with open(config_path, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加載配置文件失敗: {str(e)}")
        return None

def setup_args():
    """設置命令行參數"""
    parser = argparse.ArgumentParser(description="測試購票流程腳本")
    
    # 基本參數
    parser.add_argument("--url", type=str, help="活動 URL")
    parser.add_argument("--username", type=str, help="TicketPlus 帳號")
    parser.add_argument("--password", type=str, help="TicketPlus 密碼")
    
    # 票券參數
    parser.add_argument("--areas", type=str, nargs="+", help="目標區域列表，按優先順序排序")
    parser.add_argument("--count", type=int, help="票數")
    
    # 其他參數
    parser.add_argument("--headless", action="store_true", help="是否使用無頭模式（不顯示瀏覽器界面）")
    
    return parser.parse_args()

def main():
    """主函數"""
    # 解析命令行參數
    args = setup_args()
    
    # 加載配置文件
    config = load_config()
    if config is None:
        logger.error("無法加載配置文件，程序退出")
        return 1
    
    # 獲取帳號密碼
    username = args.username or config.get("ticketplus_username", "")
    password = args.password or config.get("ticketplus_password", "")
    
    if not username or not password:
        logger.error("請提供 TicketPlus 帳號和密碼")
        return 1
    
    # 獲取活動 URL
    event_url = args.url
    if not event_url:
        # 使用配置文件中的第一個 URL
        if config.get("urls") and len(config.get("urls")) > 0:
            event_url = config["urls"][0]
        else:
            logger.error("請提供活動 URL")
            return 1
    
    # 獲取目標區域和票數
    auto_purchase_config = config.get("auto_purchase", {})
    target_areas = args.areas or auto_purchase_config.get("target_areas", [])
    ticket_count = args.count or auto_purchase_config.get("ticket_count", 2)
    
    if not target_areas:
        logger.error("請提供目標區域")
        return 1
    
    # 獲取其他參數
    headless = args.headless
    
    # 創建自動搶票實例
    auto_purchase = TicketPlusAutoPurchase(username, password, headless=headless)
    
    try:
        # 執行測試購票流程
        logger.info(f"開始測試購票流程: {event_url}")
        logger.info(f"目標區域: {', '.join(target_areas)}")
        logger.info(f"票數: {ticket_count}")
        
        result = auto_purchase.test_purchase_flow(
            event_url,
            target_areas,
            ticket_count
        )
        
        # 輸出測試結果
        logger.info("測試結果:")
        logger.info(f"成功: {result['success']}")
        logger.info(f"訊息: {result['message']}")
        logger.info(f"完成步驟: {', '.join(result['steps_completed'])}")
        logger.info(f"當前 URL: {result['current_url']}")
        
        if result['success']:
            logger.info("測試成功！已成功進入結帳頁面")
            print("\n✅ 測試成功！已成功進入結帳頁面")
            print(f"完成步驟: {', '.join(result['steps_completed'])}")
            print(f"當前 URL: {result['current_url']}")
        else:
            logger.error("測試失敗")
            print("\n❌ 測試失敗")
            print(f"訊息: {result['message']}")
            print(f"完成步驟: {', '.join(result['steps_completed'])}")
            print(f"當前 URL: {result['current_url']}")
            return 1
        
        return 0
    except KeyboardInterrupt:
        logger.info("用戶中斷程序")
        return 1
    except Exception as e:
        logger.error(f"發生錯誤: {str(e)}")
        return 1
    finally:
        # 等待用戶輸入，然後關閉瀏覽器
        input("\n按 Enter 鍵關閉瀏覽器...")
        auto_purchase.close()

if __name__ == "__main__":
    sys.exit(main())
