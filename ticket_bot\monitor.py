import time
import asyncio
import logging
import discord
import datetime
from discord.ext import tasks
from typing import Dict, Any, Optional

from ticket_bot.config import load_config
from ticket_bot.api import get_session_info, get_ticket_info, check_single_event
from ticket_bot.utils import format_message, taiwan_tz

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('ticket_bot.monitor')

# Global variables
previous_data = {}
last_notification_time = {}
notification_cooldown = {}

class TicketMonitor:
    def __init__(self, bot):
        self.bot = bot
        self.monitor_task = None

    def start_monitoring(self):
        """Start the monitoring task"""
        config = load_config()
        check_interval = config.get("check_interval", 30)

        # Create the task with the correct interval
        self.monitor_task = tasks.loop(seconds=check_interval)(self.monitor_tickets)

        # Start the task
        self.monitor_task.start()
        logger.info(f"Started monitoring task with interval of {check_interval} seconds")

    def update_interval(self, seconds: int):
        """Update the monitoring interval"""
        if self.monitor_task:
            self.monitor_task.change_interval(seconds=seconds)
            logger.info(f"Updated monitoring interval to {seconds} seconds")

    async def send_startup_notification(self):
        """Send a notification when the bot starts up"""
        try:
            config = load_config()
            channel_id = config.get("discord_channel_id")

            if not channel_id:
                logger.error("Discord channel ID not found in configuration")
                return

            channel = self.bot.get_channel(channel_id)
            if not channel:
                logger.error(f"Error: Could not find channel with ID {channel_id}")
                return

            # Create a startup embed with minimal information first
            embed = discord.Embed(
                title="🤖 票券監控機器人已啟動",
                description="機器人已開始監控票券狀態，將在有票券可用時發送通知。\n\n輸入 `/help` 查看可用指令。",
                color=discord.Color.blue(),
                timestamp=datetime.datetime.now(taiwan_tz)
            )

            # Add basic configuration info
            embed.add_field(
                name="⚙️ 設定資訊",
                value=f"檢查間隔: {config.get('check_interval', 30)} 秒\n" +
                      f"通知冷卻: {config.get('notification_cooldown', 3600) // 60} 分鐘\n" +
                      f"冷卻功能: {'啟用' if config.get('enable_cooldown', True) else '停用'}",
                inline=False
            )

            # Add version info
            embed.set_footer(text=f"Version: v1.0.0 | Discord API Latency: {round(self.bot.latency * 1000)} ms")

            # Send the initial notification immediately
            await channel.send(embed=embed)
            logger.info("Sent initial startup notification")

            # Now fetch event details in the background
            if config.get("urls"):
                # Create a new embed for event details
                details_embed = discord.Embed(
                    title="📋 監控中的活動",
                    description=f"目前正在監控 {len(config.get('urls', []))} 個活動",
                    color=discord.Color.blue(),
                    timestamp=datetime.datetime.now(taiwan_tz)
                )

                # Create tasks for all URLs
                tasks = []
                for event_url in config.get("urls", []):
                    tasks.append(get_session_info(event_url))

                # Run all tasks concurrently
                results = await asyncio.gather(*tasks, return_exceptions=True)

                # Process results
                events_info = ""
                for i, (event_url, result) in enumerate(zip(config.get("urls", []), results), 1):
                    if isinstance(result, Exception):
                        events_info += f"{i}. {event_url} (錯誤: {str(result)})\n"
                    elif result:
                        events_info += f"{i}. **{result['title']}**\n   {result['date']} {result['time']}\n\n"
                    else:
                        events_info += f"{i}. {event_url} (無法獲取詳細資訊)\n"

                details_embed.add_field(
                    name="活動詳情",
                    value=events_info or "無監控活動",
                    inline=False
                )

                # Send the detailed notification
                await channel.send(embed=details_embed)
                logger.info("Sent detailed event information")

        except Exception as e:
            logger.error(f"Error sending startup notification: {e}")

    async def monitor_tickets(self):
        """Monitor ticket availability and send alerts when tickets are available"""
        global previous_data, last_notification_time, notification_cooldown

        config = load_config()  # Reload config each time to allow for runtime changes
        channel = self.bot.get_channel(config["discord_channel_id"])

        if not channel:
            logger.error(f"Error: Could not find channel with ID {config['discord_channel_id']}")
            return

        # Check if this is the first run after startup
        first_run = self.monitor_task.current_loop == 0

        # Create tasks for all URLs
        tasks = []
        urls = config.get("urls", [])

        # 清除不冊配置文件中的 URL 的緩存數據
        for url in list(previous_data.keys()):
            if url not in urls:
                del previous_data[url]
                logger.info(f"Removed {url} from previous_data cache")

        for url in list(last_notification_time.keys()):
            if url not in urls:
                del last_notification_time[url]
                logger.info(f"Removed {url} from last_notification_time cache")

        for url in list(notification_cooldown.keys()):
            if url not in urls:
                del notification_cooldown[url]
                logger.info(f"Removed {url} from notification_cooldown cache")

        for event_url in urls:
            # Skip URLs in cooldown unless this is the first run
            if notification_cooldown.get(event_url, False) and not first_run:
                continue
            tasks.append(check_single_event(event_url))

        # Run all tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results
        for i, event_url in enumerate(config.get("urls", [])):
            if i >= len(results):
                continue

            result = results[i]

            try:
                if isinstance(result, Exception):
                    logger.error(f"Error checking event: {result}")
                    continue

                if not result or not result[0] or not result[1]:
                    continue

                session_info, ticket_data = result
                total_tickets = sum(ticket_data.values())
                current_time = time.time()

                # Initialize if first time seeing this URL
                if event_url not in previous_data:
                    previous_data[event_url] = ticket_data.copy()
                    last_notification_time[event_url] = 0  # Never notified

                # Force notification on first run if there are tickets available
                if first_run and total_tickets > 0:
                    embed = format_message(session_info, ticket_data)
                    try:
                        await channel.send(embed=embed)
                        logger.info(f"Initial check: Sent ticket alert for {session_info['title']} - {total_tickets} tickets available")
                        last_notification_time[event_url] = current_time
                    except Exception as e:
                        logger.error(f"Failed to send initial Discord message: {e}")
                    continue

                # Regular check (not first run)
                if total_tickets > 0:
                    # Check if we should notify based on cooldown
                    cooldown_period = config.get("notification_cooldown", 3600)  # Default 1 hour
                    time_since_last = current_time - last_notification_time.get(event_url, 0)

                    # Notify if:
                    # 1. First time seeing tickets, or
                    # 2. Ticket count changed since last check, or
                    # 3. Cooldown period has passed since last notification
                    ticket_count_changed = previous_data[event_url] != ticket_data

                    if (last_notification_time[event_url] == 0 or
                        ticket_count_changed or
                        time_since_last > cooldown_period):

                        # Send notification
                        embed = format_message(session_info, ticket_data)
                        try:
                            await channel.send(embed=embed)
                            logger.info(f"Sent ticket alert for {session_info['title']} - {total_tickets} tickets available")

                            # Update last notification time
                            last_notification_time[event_url] = current_time

                            # Apply cooldown if configured
                            if config.get("enable_cooldown", False) and not ticket_count_changed:
                                notification_cooldown[event_url] = True

                                # Schedule cooldown removal
                                async def remove_cooldown(url):
                                    await asyncio.sleep(cooldown_period)
                                    notification_cooldown[url] = False
                                    logger.info(f"Cooldown removed for {url}")

                                asyncio.create_task(remove_cooldown(event_url))
                                logger.info(f"Applied cooldown for {event_url}")

                        except Exception as e:
                            logger.error(f"Failed to send Discord message: {e}")

                # Always update previous data
                previous_data[event_url] = ticket_data.copy()

            except Exception as e:
                logger.error(f"Error processing URL {event_url}: {e}")
                continue
