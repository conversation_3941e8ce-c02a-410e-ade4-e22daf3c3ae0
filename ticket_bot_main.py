import asyncio
import discord
import logging
import os
from discord.ext import commands
from discord import app_commands

from ticket_bot.config import load_config
from monitors.ticket_api.api import initialize_session, close_session
from ticket_bot.slash_commands import TicketCommands
from monitors.ticket_api.monitor import TicketAPIMonitor
from monitors.ticket_crawler.monitor import TicketCrawlerMonitor
from ticket_bot.logging_utils import setup_logging, setup_timed_logging

# 設置日誌目錄
LOG_DIR = "/var/log/ticket_bot"

# 使用日誌輪轉功能設置日誌系統
# 這裡使用基於時間的輪轉，每天午夜輪轉一次，保留 30 天的日誌
setup_timed_logging(log_dir=LOG_DIR, when='midnight', interval=1, backup_count=30)

# 獲取日誌記錄器
logger = logging.getLogger('ticket_bot_main')

# Initialize Discord bot with proper intents
intents = discord.Intents.default()
intents.message_content = True
intents.messages = True
intents.guild_messages = True

# Create bot with slash commands only
class TicketBot(commands.Bot):
    def __init__(self):
        super().__init__(
            command_prefix=commands.when_mentioned,  # Only respond to mentions, not prefix
            intents=intents,
            help_command=None  # Disable default help command
        )

    async def setup_hook(self):
        # This will be called when the bot is ready but before on_ready
        # We'll do the actual command syncing in the on_ready event
        # This is just a placeholder for any other setup needed
        pass

# Create bot instance
bot = TicketBot()

# Create monitor instance
monitor = None

# Track if we've already completed the on_ready setup
_setup_completed = False

@bot.event
async def on_ready():
    logger.info(f'Logged in as: {bot.user}')

    # Use a global flag to ensure we only run setup once
    # This is important because Discord may trigger on_ready multiple times
    global _setup_completed, monitor
    if _setup_completed:
        logger.info("Reconnected to Discord - skipping setup")
        return

    # Initialize monitors
    api_monitor = TicketAPIMonitor(bot)
    crawler_monitor = TicketCrawlerMonitor(bot)

    # Start tasks in parallel for better performance
    setup_tasks = [
        asyncio.create_task(register_slash_commands()),
        asyncio.create_task(api_monitor.send_startup_notification()),
        asyncio.create_task(crawler_monitor.send_startup_notification())
    ]

    # Start monitoring immediately - doesn't need to wait for other tasks
    api_monitor.start_monitoring()
    crawler_monitor.start_monitoring()

    # Wait for all setup tasks to complete
    await asyncio.gather(*setup_tasks)

    # Mark setup as completed
    _setup_completed = True

    logger.info("Bot is ready and monitoring has started")

async def register_slash_commands():
    """Register all slash commands"""
    # Only clear and register commands if needed
    # This is a performance optimization to avoid unnecessary API calls

    # Get the config to check if we're in development mode
    config = load_config()
    dev_mode = config.get("dev_mode", False)
    dev_guild_id = config.get("dev_guild_id", None)

    # In development mode, we only sync to a specific guild for faster testing
    # In production mode, we sync globally but less frequently
    if dev_mode and dev_guild_id:
        # Development mode - sync to specific guild
        guild = discord.Object(id=dev_guild_id)
        bot.tree.clear_commands(guild=guild)
        logger.info(f"Cleared commands for development guild {dev_guild_id}")
    else:
        # Production mode - clear global commands
        bot.tree.clear_commands(guild=None)
        logger.info("Cleared global commands")

    # Register commands

    # Check command
    @bot.tree.command(name="check", description="手動檢查目前所有活動的票券狀態")
    async def check(interaction: discord.Interaction):
        await interaction.response.defer(thinking=True)
        await TicketCommands.check_tickets_slash(bot, interaction)

    # List command
    @bot.tree.command(name="list", description="列出所有監控中的活動")
    async def list_events(interaction: discord.Interaction):
        await interaction.response.defer(thinking=True)
        await TicketCommands.list_events_slash(bot, interaction)

    # Search command
    @bot.tree.command(name="search", description="搜索活動")
    @app_commands.describe(keyword="要搜索的關鍵字")
    async def search(interaction: discord.Interaction, keyword: str):
        await interaction.response.defer(thinking=True)
        await TicketCommands.search_events_slash(bot, interaction, keyword)

    # Stats command
    @bot.tree.command(name="stats", description="顯示票券統計信息")
    async def stats(interaction: discord.Interaction):
        await interaction.response.defer(thinking=True)
        await TicketCommands.show_stats_slash(bot, interaction)

    # Status command
    @bot.tree.command(name="status", description="顯示機器人當前狀態")
    async def status(interaction: discord.Interaction):
        await interaction.response.defer(thinking=True)
        await TicketCommands.show_status_slash(bot, interaction)

    # Help command
    @bot.tree.command(name="help", description="顯示幫助信息")
    async def help_command(interaction: discord.Interaction):
        await interaction.response.defer(thinking=True)
        await TicketCommands.commands_help_slash(bot, interaction)

    # Add command (admin only)
    @bot.tree.command(name="add", description="新增監控URL (管理員)")
    @app_commands.describe(url="要監控的URL")
    async def add_url(interaction: discord.Interaction, url: str):
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message("❌ 您沒有執行此指令的權限。需要管理員權限。", ephemeral=True)
            return
        await interaction.response.defer(thinking=True)
        await TicketCommands.add_url_slash(bot, interaction, url)

    # Remove command (admin only)
    @bot.tree.command(name="remove", description="移除監控URL (管理員)")
    @app_commands.describe(index="要移除的URL索引")
    async def remove_url(interaction: discord.Interaction, index: int):
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message("❌ 您沒有執行此指令的權限。需要管理員權限。", ephemeral=True)
            return
        await interaction.response.defer(thinking=True)
        await TicketCommands.remove_url_slash(bot, interaction, index)

    # Interval command (admin only)
    @bot.tree.command(name="interval", description="設定檢查間隔 (管理員)")
    @app_commands.describe(seconds="檢查間隔（秒）")
    async def set_interval(interaction: discord.Interaction, seconds: int):
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message("❌ 您沒有執行此指令的權限。需要管理員權限。", ephemeral=True)
            return
        await interaction.response.defer(thinking=True)
        await TicketCommands.set_interval_slash(bot, interaction, seconds)

    # Cooldown command (admin only)
    @bot.tree.command(name="cooldown", description="設定通知冷卻時間 (管理員)")
    @app_commands.describe(minutes="冷卻時間（分鐘）")
    async def set_cooldown(interaction: discord.Interaction, minutes: int):
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message("❌ 您沒有執行此指令的權限。需要管理員權限。", ephemeral=True)
            return
        await interaction.response.defer(thinking=True)
        await TicketCommands.set_cooldown_slash(bot, interaction, minutes)

    # Toggle cooldown command (admin only)
    @bot.tree.command(name="toggle_cooldown", description="開關冷卻功能 (管理員)")
    async def toggle_cooldown(interaction: discord.Interaction):
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message("❌ 您沒有執行此指令的權限。需要管理員權限。", ephemeral=True)
            return
        await interaction.response.defer(thinking=True)
        await TicketCommands.toggle_cooldown_slash(bot, interaction)

    # Test notification command (admin only)
    @bot.tree.command(name="notify_test", description="測試通知 (管理員)")
    @app_commands.describe(index="要測試的活動索引 (可選)")
    async def test_notification(interaction: discord.Interaction, index: int = None):
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message("❌ 您沒有執行此指令的權限。需要管理員權限。", ephemeral=True)
            return
        await interaction.response.defer(thinking=True)
        await TicketCommands.test_notification_slash(bot, interaction, index)

    # Clear messages command (admin only)
    @bot.tree.command(name="clear", description="清除指定數量的訊息 (管理員)")
    @app_commands.describe(amount="要清除的訊息數量")
    async def clear_messages(interaction: discord.Interaction, amount: int = 10):
        if not interaction.user.guild_permissions.manage_messages:
            await interaction.response.send_message("❌ 您沒有執行此指令的權限。需要管理訊息權限。", ephemeral=True)
            return
        await interaction.response.defer(thinking=True, ephemeral=True)
        await TicketCommands.clear_messages_slash(bot, interaction, amount)

    # Clear bot messages command (admin only)
    @bot.tree.command(name="clear_bot", description="清除機器人訊息 (管理員)")
    @app_commands.describe(amount="要清除的訊息數量")
    async def clear_bot_messages(interaction: discord.Interaction, amount: int = 50):
        if not interaction.user.guild_permissions.manage_messages:
            await interaction.response.send_message("❌ 您沒有執行此指令的權限。需要管理訊息權限。", ephemeral=True)
            return
        await interaction.response.defer(thinking=True, ephemeral=True)
        await TicketCommands.clear_bot_messages_slash(bot, interaction, amount)

    # Sync commands - use the same guild object if in dev mode
    try:
        if dev_mode and dev_guild_id:
            # Sync to specific guild in development mode
            await bot.tree.sync(guild=guild)
            logger.info(f"Synced commands to development guild {dev_guild_id}")
        else:
            # Sync globally in production mode
            await bot.tree.sync()
            logger.info("Synced commands globally")
    except discord.errors.HTTPException as e:
        if e.code == 429:  # Rate limit error
            logger.warning(f"Rate limited while syncing commands: {e}")
            # Wait a bit and try again with fewer syncs
            await asyncio.sleep(5)
            await bot.tree.sync()
            logger.info("Synced commands after rate limit delay")
        else:
            logger.error(f"HTTP error while syncing commands: {e}")
    except Exception as e:
        logger.error(f"Error syncing commands: {e}")

    logger.info("All slash commands registered and synced")

@bot.event
async def on_command_error(ctx, error):
    """Handle prefix command errors (not used with slash commands)"""
    logger.error(f"Command error: {error}")

@bot.tree.error
async def on_app_command_error(interaction: discord.Interaction, error: app_commands.AppCommandError):
    """Handle slash command errors"""
    if isinstance(error, app_commands.CommandNotFound):
        await interaction.response.send_message("❌ 未知指令。請使用 `/help` 查看可用指令。", ephemeral=True)
    elif isinstance(error, app_commands.MissingPermissions):
        await interaction.response.send_message("❌ 您沒有執行此指令的權限。", ephemeral=True)
    elif isinstance(error, app_commands.TransformerError):
        await interaction.response.send_message(f"❌ 參數類型錯誤: {error}", ephemeral=True)
    else:
        logger.error(f"Slash command error: {error}")
        await interaction.response.send_message(f"❌ 執行指令時發生錯誤: {error}", ephemeral=True)

async def cleanup():
    """Cleanup resources before shutdown"""
    try:
        # Close aiohttp session
        await close_session()

        # Close bot if it's running
        if not bot.is_closed():
            await bot.close()

        logger.info("All resources cleaned up successfully")
    except Exception as e:
        logger.error(f"Error during cleanup: {e}")

async def main():
    """Main entry point for the bot"""
    try:
        # Load configuration
        config = load_config()

        # Check if configuration is valid
        if "discord_token" not in config or not config["discord_token"]:
            logger.error("Discord token not found in configuration")
            return

        if "discord_channel_id" not in config or not config["discord_channel_id"]:
            logger.error("Discord channel ID not found in configuration")
            return

        # Initialize API session
        await initialize_session()

        # Run the bot
        logger.info("Starting bot...")
        await bot.start(config["discord_token"])
    except KeyboardInterrupt:
        logger.info("Bot shutdown requested by keyboard interrupt")
    except asyncio.CancelledError:
        logger.info("Bot shutdown requested by task cancellation")
    except Exception as e:
        logger.error(f"Error running bot: {e}")
    finally:
        # Ensure we clean up resources
        logger.info("Cleaning up resources...")
        await cleanup()
        logger.info("Bot shutdown complete")

if __name__ == "__main__":
    # Set up proper signal handling for Windows
    import signal
    import sys

    def signal_handler(sig, frame):
        logger.info(f"Received signal {sig}, shutting down...")
        if sys.platform == 'win32':
            # On Windows, KeyboardInterrupt doesn't always work as expected
            # So we need to force the event loop to stop
            loop = asyncio.get_event_loop()
            if loop.is_running():
                loop.stop()
        sys.exit(0)

    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # Termination signal

    try:
        # Run the bot with proper exception handling
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received, exiting...")
    except Exception as e:
        logger.error(f"Unhandled exception: {e}")
        import traceback
        traceback.print_exc()
