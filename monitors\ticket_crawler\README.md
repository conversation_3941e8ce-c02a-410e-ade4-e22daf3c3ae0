# 票券爬蟲監控模組

## 概述
此模組透過網頁爬蟲技術監控各種票務網站的票券可用性，適用於沒有公開 API 或 API 限制較嚴格的票務平台。

## 計劃支持的票務網站

### 已計劃支持
- [ ] **TicketPlus (拓元售票)** - 作為 API 監控的備用方案
- [ ] **KKTIX** - 獨立音樂、藝文活動主要平台
- [ ] **FamiTicket (全家便利商店售票)** - 便利商店售票系統
- [ ] **ibon (7-11 售票)** - 7-11 便利商店售票系統
- [ ] **年代售票** - 大型演唱會、體育賽事
- [ ] **寬宏售票** - 展覽、演出活動

### 技術特色
- 🔄 **智能反爬蟲對策** - 隨機 User-Agent、請求間隔、代理輪換
- 🎯 **精確元素定位** - 支持多種選擇器策略
- 📊 **結構化數據提取** - 統一的票券信息格式
- ⚡ **高效並發處理** - 異步爬蟲，支持批量監控
- 🛡️ **錯誤恢復機制** - 自動重試、降級策略
- 📝 **詳細日誌記錄** - 便於調試和監控

## 目錄結構
```
monitors/ticket_crawler/
├── __init__.py              # 模組初始化
├── README.md               # 說明文件
├── crawler.py              # 爬蟲基礎類別
├── monitor.py              # 監控管理器
├── sites/                  # 各網站專用爬蟲
│   ├── __init__.py
│   ├── base.py            # 基礎爬蟲類別
│   ├── ticketplus.py      # TicketPlus 爬蟲
│   ├── kktix.py           # KKTIX 爬蟲
│   ├── famiticket.py      # FamiTicket 爬蟲
│   ├── ibon.py            # ibon 爬蟲
│   ├── era.py             # 年代售票爬蟲
│   └── kham.py            # 寬宏售票爬蟲
├── utils/                  # 工具函數
│   ├── __init__.py
│   ├── selectors.py       # 選擇器工具
│   ├── parsers.py         # 數據解析工具
│   └── anti_detection.py  # 反爬蟲檢測工具
└── config/                 # 配置文件
    ├── __init__.py
    ├── sites.json         # 網站配置
    └── selectors.json     # 選擇器配置
```

## 使用方式

### 基本配置
```json
{
  "ticket_crawler": {
    "enabled": true,
    "sites": {
      "kktix": {
        "enabled": true,
        "urls": [
          "https://kktix.com/events/example-event"
        ],
        "check_interval": 60,
        "max_retries": 3
      },
      "famiticket": {
        "enabled": false,
        "urls": [],
        "check_interval": 120
      }
    },
    "anti_detection": {
      "random_delay": [1, 5],
      "user_agents_rotation": true,
      "proxy_rotation": false
    }
  }
}
```

### Discord 指令
```
/crawler_add <site> <url>     # 新增爬蟲監控
/crawler_remove <site> <url>  # 移除爬蟲監控
/crawler_status               # 查看爬蟲狀態
/crawler_test <site> <url>    # 測試爬蟲功能
```

## 開發計劃

### Phase 1: 基礎架構
- [ ] 實現基礎爬蟲類別
- [ ] 建立監控管理器
- [ ] 設計統一的數據格式

### Phase 2: 網站支持
- [ ] 實現 KKTIX 爬蟲
- [ ] 實現 FamiTicket 爬蟲
- [ ] 實現 ibon 爬蟲

### Phase 3: 進階功能
- [ ] 反爬蟲檢測對策
- [ ] 代理輪換支持
- [ ] 驗證碼處理

### Phase 4: 優化與維護
- [ ] 性能優化
- [ ] 錯誤處理改進
- [ ] 監控儀表板

## 注意事項

⚠️ **法律與道德考量**
- 請遵守各網站的 robots.txt 和服務條款
- 合理控制請求頻率，避免對目標網站造成負擔
- 僅用於個人監控用途，不得用於商業目的
- 尊重網站的反爬蟲措施

⚠️ **技術限制**
- 網站結構變更可能導致爬蟲失效
- 某些網站可能有嚴格的反爬蟲機制
- 需要定期維護和更新選擇器

⚠️ **使用風險**
- IP 可能被封鎖
- 帳戶可能被限制
- 請謹慎使用並承擔相關風險
