"""
數據解析工具
提供各種數據格式的解析和標準化功能
"""

import re
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

logger = logging.getLogger('monitors.ticket_crawler.parsers')

@dataclass
class ParsedDateTime:
    """解析後的日期時間"""
    date: str           # 日期 (YYYY-MM-DD)
    time: str           # 時間 (HH:MM)
    datetime_obj: Optional[datetime] = None
    original_text: str = ""

@dataclass
class ParsedPrice:
    """解析後的價格"""
    amount: int         # 金額
    currency: str       # 貨幣
    original_text: str  # 原始文本

@dataclass
class ParsedTicketCount:
    """解析後的票數"""
    available: Optional[int] = None    # 可用票數
    total: Optional[int] = None        # 總票數
    status_text: str = ""              # 狀態文本
    is_sold_out: bool = False          # 是否售完

class DataParser:
    """
    數據解析器
    提供各種票務數據的解析和標準化功能
    """
    
    @staticmethod
    def parse_datetime(text: str) -> ParsedDateTime:
        """
        解析日期時間文本
        
        支持格式：
        - 2025/08/23 (Sat.) 19:00
        - 2025-08-23 19:00
        - 8月23日 19:00
        - 2025年8月23日 下午7:00
        
        Args:
            text: 日期時間文本
            
        Returns:
            ParsedDateTime: 解析結果
        """
        text = text.strip()
        
        # 模式1: 2025/08/23 (Sat.) 19:00
        pattern1 = r'(\d{4})[/-](\d{1,2})[/-](\d{1,2}).*?(\d{1,2}):(\d{2})'
        match1 = re.search(pattern1, text)
        if match1:
            year, month, day, hour, minute = match1.groups()
            date_str = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
            time_str = f"{hour.zfill(2)}:{minute}"
            
            try:
                dt_obj = datetime(int(year), int(month), int(day), int(hour), int(minute))
                return ParsedDateTime(
                    date=date_str,
                    time=time_str,
                    datetime_obj=dt_obj,
                    original_text=text
                )
            except ValueError:
                pass
        
        # 模式2: 8月23日 19:00 (需要年份推測)
        pattern2 = r'(\d{1,2})月(\d{1,2})日.*?(\d{1,2}):(\d{2})'
        match2 = re.search(pattern2, text)
        if match2:
            month, day, hour, minute = match2.groups()
            current_year = datetime.now().year
            
            # 如果月份已過，推測為明年
            current_month = datetime.now().month
            year = current_year + 1 if int(month) < current_month else current_year
            
            date_str = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
            time_str = f"{hour.zfill(2)}:{minute}"
            
            try:
                dt_obj = datetime(year, int(month), int(day), int(hour), int(minute))
                return ParsedDateTime(
                    date=date_str,
                    time=time_str,
                    datetime_obj=dt_obj,
                    original_text=text
                )
            except ValueError:
                pass
        
        # 模式3: 下午時間轉換
        if '下午' in text or 'PM' in text.upper():
            # 提取時間並轉換為24小時制
            time_match = re.search(r'(\d{1,2}):(\d{2})', text)
            if time_match:
                hour, minute = time_match.groups()
                hour = int(hour)
                if hour < 12:  # 下午時間需要加12
                    hour += 12
                # 重新構造文本進行解析
                modified_text = re.sub(r'\d{1,2}:\d{2}', f'{hour:02d}:{minute}', text)
                modified_text = modified_text.replace('下午', '').replace('PM', '')
                return DataParser.parse_datetime(modified_text)
        
        # 無法解析，返回默認值
        logger.warning(f"Cannot parse datetime: {text}")
        return ParsedDateTime(
            date="TBD",
            time="TBD",
            original_text=text
        )
    
    @staticmethod
    def parse_price(text: str) -> ParsedPrice:
        """
        解析價格文本
        
        支持格式：
        - NT$1200
        - $1200
        - 1200元
        - 1200區
        - 1,200
        
        Args:
            text: 價格文本
            
        Returns:
            ParsedPrice: 解析結果
        """
        text = text.strip()
        
        # 移除逗號
        text = text.replace(',', '')
        
        # 提取數字
        numbers = re.findall(r'\d+', text)
        if not numbers:
            return ParsedPrice(amount=0, currency="TWD", original_text=text)
        
        amount = int(numbers[0])
        
        # 判斷貨幣
        currency = "TWD"  # 默認台幣
        if 'USD' in text.upper() or 'US$' in text:
            currency = "USD"
        elif 'HKD' in text.upper() or 'HK$' in text:
            currency = "HKD"
        elif 'CNY' in text.upper() or 'RMB' in text.upper():
            currency = "CNY"
        
        return ParsedPrice(
            amount=amount,
            currency=currency,
            original_text=text
        )
    
    @staticmethod
    def parse_ticket_count(text: str) -> ParsedTicketCount:
        """
        解析票數文本
        
        支持格式：
        - 33 seat(s) remaining
        - 50張剩餘
        - Sold out
        - 售完
        - 已售完
        - 搶購一空
        
        Args:
            text: 票數文本
            
        Returns:
            ParsedTicketCount: 解析結果
        """
        text = text.strip()
        text_lower = text.lower()
        
        # 檢查售完狀態
        sold_out_keywords = [
            'sold out', 'sold-out', '售完', '已售完', '搶購一空', 
            '無票', '沒票', '完售', '銷售一空'
        ]
        
        is_sold_out = any(keyword in text_lower for keyword in sold_out_keywords)
        
        if is_sold_out:
            return ParsedTicketCount(
                available=0,
                status_text=text,
                is_sold_out=True
            )
        
        # 提取剩餘票數
        # 模式1: 33 seat(s) remaining
        pattern1 = r'(\d+)\s+seat\(s\)\s+remaining'
        match1 = re.search(pattern1, text_lower)
        if match1:
            available = int(match1.group(1))
            return ParsedTicketCount(
                available=available,
                status_text=text,
                is_sold_out=False
            )
        
        # 模式2: 50張剩餘
        pattern2 = r'(\d+)\s*張.*?剩'
        match2 = re.search(pattern2, text)
        if match2:
            available = int(match2.group(1))
            return ParsedTicketCount(
                available=available,
                status_text=text,
                is_sold_out=False
            )
        
        # 模式3: 剩餘50張
        pattern3 = r'剩.*?(\d+)\s*張'
        match3 = re.search(pattern3, text)
        if match3:
            available = int(match3.group(1))
            return ParsedTicketCount(
                available=available,
                status_text=text,
                is_sold_out=False
            )
        
        # 模式4: 僅有數字
        numbers = re.findall(r'\d+', text)
        if numbers:
            # 如果有數字但沒有明確的售完標識，假設為可用票數
            available = int(numbers[0])
            return ParsedTicketCount(
                available=available,
                status_text=text,
                is_sold_out=available == 0
            )
        
        # 無法解析，返回未知狀態
        return ParsedTicketCount(
            status_text=text,
            is_sold_out=False
        )
    
    @staticmethod
    def parse_area_name(text: str) -> str:
        """
        解析區域名稱
        
        從包含價格和狀態的文本中提取純粹的區域名稱
        
        Args:
            text: 包含區域信息的文本
            
        Returns:
            str: 清理後的區域名稱
        """
        text = text.strip()
        
        # 移除價格信息 (如 "2800區", "NT$1200")
        text = re.sub(r'\d+區', '', text)
        text = re.sub(r'NT?\$\d+', '', text)
        text = re.sub(r'\$\d+', '', text)
        text = re.sub(r'\d+元', '', text)
        
        # 移除票數信息
        text = re.sub(r'\d+\s+seat\(s\)\s+remaining', '', text, flags=re.IGNORECASE)
        text = re.sub(r'\d+\s*張.*?剩', '', text)
        text = re.sub(r'剩.*?\d+\s*張', '', text)
        
        # 移除售完信息
        text = re.sub(r'sold\s+out', '', text, flags=re.IGNORECASE)
        text = re.sub(r'售完|已售完|搶購一空', '', text)
        
        # 清理多餘空格
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    @staticmethod
    def normalize_venue_name(text: str) -> str:
        """
        標準化場地名稱
        
        Args:
            text: 場地名稱文本
            
        Returns:
            str: 標準化後的場地名稱
        """
        text = text.strip()
        
        # 常見場地名稱標準化
        venue_mappings = {
            '高雄巨蛋': '高雄巨蛋',
            '小巨蛋': '台北小巨蛋',
            '台北小巨蛋': '台北小巨蛋',
            '台北流行音樂中心': '台北流行音樂中心',
            '南港展覽館': '南港展覽館',
            '世貿': '世貿展覽館',
        }
        
        for key, value in venue_mappings.items():
            if key in text:
                return value
        
        return text
    
    @staticmethod
    def extract_event_id_patterns() -> Dict[str, str]:
        """
        獲取各網站的活動ID提取模式
        
        Returns:
            Dict[str, str]: 網站名稱到正則表達式的映射
        """
        return {
            'tixcraft': r'/(?:activity/detail|ticket/area)/([^/?]+)',
            'kktix': r'/events/([^/?]+)',
            'famiticket': r'/event/([^/?]+)',
            'ibon': r'/event/([^/?]+)',
        }
    
    @staticmethod
    def clean_html_text(text: str) -> str:
        """
        清理HTML文本
        
        Args:
            text: 包含HTML的文本
            
        Returns:
            str: 清理後的純文本
        """
        import html
        
        # 解碼HTML實體
        text = html.unescape(text)
        
        # 移除HTML標籤
        text = re.sub(r'<[^>]+>', '', text)
        
        # 清理多餘空白
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
