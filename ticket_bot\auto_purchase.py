"""
自動搶票功能模組
"""
import os
import time
import logging
import asyncio
import json
import sys
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementClickInterceptedException
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service

# 嘗試導入 webdriver_manager，如果不可用則使用本地 ChromeDriver
try:
    from webdriver_manager.chrome import ChromeDriverManager
    WEBDRIVER_MANAGER_AVAILABLE = True
except ImportError:
    WEBDRIVER_MANAGER_AVAILABLE = False

# 配置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('ticket_bot.auto_purchase')

class TicketPlusAutoPurchase:
    """TicketPlus 自動搶票類"""

    def __init__(self, username, password, headless=False):
        """
        初始化

        Args:
            username (str): TicketPlus 帳號
            password (str): TicketPlus 密碼
            headless (bool): 是否使用無頭模式（不顯示瀏覽器界面）
        """
        self.username = username
        self.password = password
        self.headless = headless
        self.driver = None
        self.logged_in = False

    def initialize_driver(self):
        """初始化 WebDriver"""
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument("--headless")

        # 添加其他選項以提高性能和穩定性
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-notifications")
        chrome_options.add_argument("--disable-infobars")

        # 初始化 Chrome WebDriver
        if WEBDRIVER_MANAGER_AVAILABLE:
            # 使用 webdriver_manager 自動下載和管理 ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            logger.info("使用 webdriver_manager 初始化 WebDriver")
        else:
            # 使用本地 ChromeDriver
            # 嘗試在不同的位置查找 ChromeDriver
            chromedriver_paths = [
                # 當前目錄
                os.path.join(os.getcwd(), "chromedriver.exe"),
                os.path.join(os.getcwd(), "chromedriver"),
                # webdriver 子目錄
                os.path.join(os.getcwd(), "webdriver", "chromedriver.exe"),
                os.path.join(os.getcwd(), "webdriver", "chromedriver"),
                # 上級目錄的 webdriver 子目錄
                os.path.join(os.path.dirname(os.getcwd()), "webdriver", "chromedriver.exe"),
                os.path.join(os.path.dirname(os.getcwd()), "webdriver", "chromedriver"),
                # 系統路徑
                "chromedriver.exe",
                "chromedriver"
            ]

            # 查找可用的 ChromeDriver
            chromedriver_path = None
            for path in chromedriver_paths:
                if os.path.exists(path):
                    chromedriver_path = path
                    break

            if chromedriver_path:
                logger.info(f"使用本地 ChromeDriver: {chromedriver_path}")
                service = Service(chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                # 如果找不到 ChromeDriver，嘗試直接使用 Chrome
                logger.warning("找不到 ChromeDriver，嘗試直接使用 Chrome")
                self.driver = webdriver.Chrome(options=chrome_options)

        self.driver.maximize_window()
        logger.info("WebDriver 初始化完成")

    def login(self):
        """登入 TicketPlus"""
        if self.driver is None:
            self.initialize_driver()

        try:
            # 前往登入頁面
            self.driver.get("https://ticketplus.com.tw/users/sign_in")
            logger.info("已前往登入頁面")

            # 等待頁面加載
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.NAME, "email"))
            )

            # 輸入帳號密碼
            self.driver.find_element(By.NAME, "email").send_keys(self.username)
            self.driver.find_element(By.NAME, "password").send_keys(self.password)
            logger.info("已輸入帳號密碼")

            # 點擊登入按鈕
            login_button = self.driver.find_element(By.XPATH, "//button[contains(text(), '登入')]")
            login_button.click()
            logger.info("已點擊登入按鈕")

            # 等待登入成功
            WebDriverWait(self.driver, 10).until(
                EC.url_contains("ticketplus.com.tw/user")
            )

            self.logged_in = True
            logger.info("登入成功")
            return True

        except TimeoutException:
            logger.error("登入超時")
            return False
        except Exception as e:
            logger.error(f"登入失敗: {str(e)}")
            return False

    def go_to_event(self, event_url):
        """
        前往活動頁面

        Args:
            event_url (str): 活動 URL

        Returns:
            bool: 是否成功前往活動頁面
        """
        if self.driver is None:
            self.initialize_driver()

        if not self.logged_in:
            if not self.login():
                return False

        try:
            # 前往活動頁面
            self.driver.get(event_url)
            logger.info(f"已前往活動頁面: {event_url}")

            # 等待頁面加載
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CLASS_NAME, "event-title"))
            )

            return True

        except TimeoutException:
            logger.error("活動頁面加載超時")
            return False
        except Exception as e:
            logger.error(f"前往活動頁面失敗: {str(e)}")
            return False

    def select_area_and_tickets(self, target_areas, ticket_count):
        """
        選擇區域和票數

        Args:
            target_areas (list): 目標區域列表，按優先順序排序
            ticket_count (int): 票數

        Returns:
            bool: 是否成功選擇區域和票數
        """
        try:
            # 等待區域選擇元素加載
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CLASS_NAME, "ticket-area"))
            )

            # 獲取所有可用區域
            area_elements = self.driver.find_elements(By.CLASS_NAME, "ticket-area")

            # 檢查每個目標區域
            selected_area = False
            for target_area in target_areas:
                for area_element in area_elements:
                    area_name = area_element.find_element(By.CLASS_NAME, "area-name").text

                    # 檢查區域名稱是否匹配
                    if target_area in area_name:
                        # 檢查是否有票
                        try:
                            count_element = area_element.find_element(By.CLASS_NAME, "ticket-count")
                            count_text = count_element.text

                            # 如果有票，點擊該區域
                            if "售完" not in count_text and "已售完" not in count_text:
                                area_element.click()
                                logger.info(f"已選擇區域: {area_name}")
                                selected_area = True
                                break
                        except NoSuchElementException:
                            # 如果找不到票數元素，可能是因為有票
                            area_element.click()
                            logger.info(f"已選擇區域: {area_name}")
                            selected_area = True
                            break

                if selected_area:
                    break

            if not selected_area:
                logger.error("所有目標區域都沒有票")
                return False

            # 選擇票數
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CLASS_NAME, "ticket-quantity"))
            )

            # 找到票數選擇元素
            quantity_select = self.driver.find_element(By.CLASS_NAME, "ticket-quantity")
            quantity_select.click()

            # 選擇指定票數
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, f"//li[@data-value='{ticket_count}']"))
            )

            quantity_option = self.driver.find_element(By.XPATH, f"//li[@data-value='{ticket_count}']")
            quantity_option.click()
            logger.info(f"已選擇票數: {ticket_count}")

            return True

        except TimeoutException:
            logger.error("選擇區域和票數超時")
            return False
        except NoSuchElementException as e:
            logger.error(f"找不到元素: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"選擇區域和票數失敗: {str(e)}")
            return False

    def proceed_to_checkout(self):
        """
        進入結帳流程

        Returns:
            bool: 是否成功進入結帳流程
        """
        try:
            # 點擊下一步按鈕
            next_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '下一步')]"))
            )
            next_button.click()
            logger.info("已點擊下一步按鈕")

            # 等待頁面加載
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CLASS_NAME, "checkout-form"))
            )

            return True

        except TimeoutException:
            logger.error("進入結帳流程超時")
            return False
        except ElementClickInterceptedException:
            logger.error("下一步按鈕被遮擋，無法點擊")
            return False
        except Exception as e:
            logger.error(f"進入結帳流程失敗: {str(e)}")
            return False

    def fill_checkout_form(self, buyer_info=None):
        """
        填寫結帳表單

        Args:
            buyer_info (dict): 購票人信息，包含姓名、電話、電子郵件等

        Returns:
            bool: 是否成功填寫結帳表單
        """
        if buyer_info is None:
            # 使用默認值（已登入的用戶信息）
            buyer_info = {}

        try:
            # 檢查是否有需要填寫的表單
            form_elements = self.driver.find_elements(By.CLASS_NAME, "form-control")

            if len(form_elements) == 0:
                logger.info("沒有需要填寫的表單，可能已經自動填充")
                return True

            # 填寫表單
            for element in form_elements:
                field_name = element.get_attribute("name")

                if field_name in buyer_info:
                    element.clear()
                    element.send_keys(buyer_info[field_name])
                    logger.info(f"已填寫 {field_name}: {buyer_info[field_name]}")

            # 勾選同意條款
            try:
                agree_checkbox = self.driver.find_element(By.XPATH, "//input[@type='checkbox']")
                if not agree_checkbox.is_selected():
                    agree_checkbox.click()
                    logger.info("已勾選同意條款")
            except NoSuchElementException:
                logger.info("沒有找到同意條款勾選框")

            return True

        except Exception as e:
            logger.error(f"填寫結帳表單失敗: {str(e)}")
            return False

    def confirm_purchase(self, auto_confirm=False):
        """
        確認購買

        Args:
            auto_confirm (bool): 是否自動確認購買（謹慎使用）

        Returns:
            bool: 是否成功確認購買
        """
        try:
            # 找到確認購買按鈕
            confirm_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '確認購買')]"))
            )

            if auto_confirm:
                # 自動點擊確認購買按鈕
                confirm_button.click()
                logger.info("已點擊確認購買按鈕")

                # 等待購買結果
                WebDriverWait(self.driver, 30).until(
                    EC.url_contains("order-completed")
                )

                logger.info("購買成功")
                return True
            else:
                # 不自動確認，等待用戶手動確認
                logger.info("請手動點擊確認購買按鈕")

                # 等待 5 分鐘，讓用戶有足夠時間手動確認
                WebDriverWait(self.driver, 300).until(
                    EC.url_contains("order-completed")
                )

                logger.info("購買完成")
                return True

        except TimeoutException:
            logger.error("確認購買超時")
            return False
        except Exception as e:
            logger.error(f"確認購買失敗: {str(e)}")
            return False

    def auto_purchase(self, event_url, target_areas, ticket_count, buyer_info=None, auto_confirm=False):
        """
        自動購票主流程

        Args:
            event_url (str): 活動 URL
            target_areas (list): 目標區域列表，按優先順序排序
            ticket_count (int): 票數
            buyer_info (dict): 購票人信息，包含姓名、電話、電子郵件等
            auto_confirm (bool): 是否自動確認購買（謹慎使用）

        Returns:
            bool: 是否成功購票
        """
        try:
            # 初始化 WebDriver
            if self.driver is None:
                self.initialize_driver()

            # 登入
            if not self.logged_in:
                if not self.login():
                    return False

            # 前往活動頁面
            if not self.go_to_event(event_url):
                return False

            # 選擇區域和票數
            if not self.select_area_and_tickets(target_areas, ticket_count):
                return False

            # 進入結帳流程
            if not self.proceed_to_checkout():
                return False

            # 填寫結帳表單
            if not self.fill_checkout_form(buyer_info):
                return False

            # 確認購買
            if not self.confirm_purchase(auto_confirm):
                return False

            return True

        except Exception as e:
            logger.error(f"自動購票失敗: {str(e)}")
            return False
        finally:
            # 不關閉瀏覽器，讓用戶可以看到結果
            pass

    def test_purchase_flow(self, event_url, target_areas, ticket_count):
        """
        測試購票流程，只執行到選擇區域、票數並點擊下一步按鈕的步驟

        Args:
            event_url (str): 活動 URL
            target_areas (list): 目標區域列表，按優先順序排序
            ticket_count (int): 票數

        Returns:
            dict: 測試結果，包含成功/失敗狀態和詳細信息
        """
        result = {
            "success": False,
            "message": "",
            "steps_completed": [],
            "current_url": ""
        }

        try:
            # 初始化 WebDriver
            if self.driver is None:
                self.initialize_driver()
                result["steps_completed"].append("初始化 WebDriver")

            # 登入
            if not self.logged_in:
                if not self.login():
                    result["message"] = "登入失敗"
                    return result
                result["steps_completed"].append("登入成功")

            # 前往活動頁面
            if not self.go_to_event(event_url):
                result["message"] = "前往活動頁面失敗"
                return result
            result["steps_completed"].append("前往活動頁面成功")
            result["current_url"] = self.driver.current_url

            # 選擇區域和票數
            if not self.select_area_and_tickets(target_areas, ticket_count):
                result["message"] = "選擇區域和票數失敗"
                return result
            result["steps_completed"].append("選擇區域和票數成功")

            # 點擊下一步按鈕
            try:
                # 點擊下一步按鈕
                next_button = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '下一步')]"))
                )
                next_button.click()
                logger.info("已點擊下一步按鈕")
                result["steps_completed"].append("點擊下一步按鈕成功")

                # 等待頁面加載
                time.sleep(2)  # 給頁面一些加載時間
                result["current_url"] = self.driver.current_url

                # 檢查是否進入了結帳頁面
                if "checkout" in self.driver.current_url.lower() or "order" in self.driver.current_url.lower():
                    result["success"] = True
                    result["message"] = "成功進入結帳頁面"
                else:
                    result["message"] = "點擊下一步後未進入結帳頁面"

            except TimeoutException:
                result["message"] = "找不到下一步按鈕或點擊超時"
            except ElementClickInterceptedException:
                result["message"] = "下一步按鈕被遮擋，無法點擊"
            except Exception as e:
                result["message"] = f"點擊下一步按鈕時發生錯誤: {str(e)}"

            return result

        except Exception as e:
            result["message"] = f"測試購票流程失敗: {str(e)}"
            return result

    def close(self):
        """關閉 WebDriver"""
        if self.driver is not None:
            self.driver.quit()
            self.driver = None
            self.logged_in = False
            logger.info("WebDriver 已關閉")

# 測試代碼
if __name__ == "__main__":
    # 從配置文件讀取帳號密碼
    config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "ticket_alert_config.json")
    with open(config_path, "r", encoding="utf-8") as f:
        config = json.load(f)

    # 獲取帳號密碼
    username = config.get("ticketplus_username", "")
    password = config.get("ticketplus_password", "")

    if not username or not password:
        print("請在配置文件中設置 ticketplus_username 和 ticketplus_password")
        exit(1)

    # 創建自動搶票實例
    auto_purchase = TicketPlusAutoPurchase(username, password, headless=False)

    # 設置目標活動和區域
    event_url = "https://ticketplus.com.tw/order/4564864f399f60d46288bebe36acdc2f/ab3b028f7e4c73bd7cbffb02c7b87d69"
    target_areas = ["搖滾區", "看台區"]
    ticket_count = 2

    # 執行自動搶票
    result = auto_purchase.auto_purchase(event_url, target_areas, ticket_count, auto_confirm=False)

    if result:
        print("自動搶票成功")
    else:
        print("自動搶票失敗")

    # 保持瀏覽器開啟，讓用戶可以看到結果
    input("按 Enter 鍵關閉瀏覽器...")
    auto_purchase.close()
