#!/usr/bin/env python3
"""
測試 Tixcraft 爬蟲功能
"""

import asyncio
import logging
import sys
from monitors.ticket_crawler.crawler import TicketCrawler
from monitors.ticket_crawler.sites.tixcraft import TixcraftCrawler

# 配置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('test_tixcraft_crawler')

async def test_tixcraft_crawler():
    """測試 Tixcraft 爬蟲功能"""
    
    # 測試URL
    test_url = "https://tixcraft.com/ticket/area/25_xalive/19055"
    
    logger.info("=== 測試 Tixcraft 爬蟲功能 ===")
    
    # 1. 測試 TixcraftCrawler 類別
    logger.info("1. 測試 TixcraftCrawler 類別")
    
    tixcraft_crawler = TixcraftCrawler()
    
    # 測試 URL 驗證
    logger.info(f"測試 URL 驗證: {test_url}")
    is_valid = tixcraft_crawler.is_valid_url(test_url)
    logger.info(f"URL 有效性: {is_valid}")
    
    # 測試活動ID提取
    event_id = tixcraft_crawler.extract_event_id(test_url)
    logger.info(f"提取的活動ID: {event_id}")
    
    # 測試網站信息
    site_info = tixcraft_crawler.get_site_info()
    logger.info(f"網站信息: {site_info}")
    
    # 測試連接
    logger.info("測試連接...")
    connection_ok = await tixcraft_crawler.test_connection()
    logger.info(f"連接狀態: {connection_ok}")
    
    # 測試爬取（注意：這裡只是測試架構，實際爬取需要實現具體邏輯）
    logger.info("測試爬取功能...")
    try:
        result = await tixcraft_crawler.crawl_event(test_url)
        logger.info(f"爬取結果: success={result.success}")
        if result.success:
            logger.info(f"活動信息: {result.event_info}")
            logger.info(f"票券區域數量: {len(result.ticket_areas)}")
            for area in result.ticket_areas:
                logger.info(f"  - {area.name}: ${area.price}, 狀態: {area.status.value}, 可用: {area.available_count}")
        else:
            logger.warning(f"爬取失敗: {result.error_message}")
    except Exception as e:
        logger.error(f"爬取過程中發生錯誤: {e}")
    
    logger.info("")
    
    # 2. 測試 TicketCrawler 管理器
    logger.info("2. 測試 TicketCrawler 管理器")
    
    crawler_manager = TicketCrawler()
    
    # 測試網站檢測
    detected_site = crawler_manager.detect_site(test_url)
    logger.info(f"檢測到的網站類型: {detected_site}")
    
    # 測試支持的網站
    supported_sites = crawler_manager.get_supported_sites()
    logger.info(f"支持的網站: {supported_sites}")
    
    # 測試獲取爬蟲
    crawler = crawler_manager.get_crawler("tixcraft")
    logger.info(f"獲取的爬蟲: {crawler.__class__.__name__ if crawler else None}")
    
    # 測試統計信息
    stats = crawler_manager.get_crawler_stats()
    logger.info(f"爬蟲統計: {stats}")
    
    # 測試單個活動爬取
    logger.info("測試通過管理器爬取...")
    try:
        result = await crawler_manager.crawl_single_event(test_url)
        logger.info(f"管理器爬取結果: success={result.success}")
        if result.success:
            logger.info(f"活動標題: {result.event_info.title}")
            logger.info(f"票券區域: {len(result.ticket_areas)} 個")
        else:
            logger.warning(f"管理器爬取失敗: {result.error_message}")
    except Exception as e:
        logger.error(f"管理器爬取過程中發生錯誤: {e}")
    
    logger.info("")
    
    # 3. 測試多個URL並發爬取
    logger.info("3. 測試多個URL並發爬取")
    
    test_urls = [
        test_url,
        "https://tixcraft.com/activity/detail/25_xalive",  # 另一種URL格式
    ]
    
    try:
        results = await crawler_manager.crawl_multiple_events(test_urls)
        logger.info(f"並發爬取完成，結果數量: {len(results)}")
        for i, result in enumerate(results):
            logger.info(f"  URL {i+1}: success={result.success}")
            if not result.success:
                logger.warning(f"    錯誤: {result.error_message}")
    except Exception as e:
        logger.error(f"並發爬取過程中發生錯誤: {e}")
    
    logger.info("")
    
    # 4. 測試所有爬蟲連接
    logger.info("4. 測試所有爬蟲連接")
    
    try:
        connection_results = await crawler_manager.test_all_crawlers()
        logger.info("所有爬蟲連接測試結果:")
        for site, status in connection_results.items():
            logger.info(f"  {site}: {'✅ 正常' if status else '❌ 異常'}")
    except Exception as e:
        logger.error(f"連接測試過程中發生錯誤: {e}")
    
    logger.info("=== 測試完成 ===")

async def test_invalid_urls():
    """測試無效URL的處理"""
    logger.info("=== 測試無效URL處理 ===")
    
    crawler_manager = TicketCrawler()
    
    invalid_urls = [
        "https://example.com/invalid",
        "https://kktix.com/events/test",  # 不支持的網站
        "invalid-url",
        "",
        None
    ]
    
    for url in invalid_urls:
        logger.info(f"測試無效URL: {url}")
        try:
            detected_site = crawler_manager.detect_site(url)
            logger.info(f"  檢測結果: {detected_site}")
            
            if detected_site:
                result = await crawler_manager.crawl_single_event(url)
                logger.info(f"  爬取結果: success={result.success}")
                if not result.success:
                    logger.info(f"  錯誤信息: {result.error_message}")
            else:
                logger.info("  無法檢測網站類型")
        except Exception as e:
            logger.error(f"  處理過程中發生錯誤: {e}")
        
        logger.info("")

def main():
    """主函數"""
    try:
        # 運行測試
        asyncio.run(test_tixcraft_crawler())
        asyncio.run(test_invalid_urls())
        
        logger.info("✅ 所有測試完成")
        
    except KeyboardInterrupt:
        logger.info("測試被用戶中斷")
    except Exception as e:
        logger.error(f"測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
