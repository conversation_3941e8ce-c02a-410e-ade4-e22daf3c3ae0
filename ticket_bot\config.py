import json
import logging
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('ticket_bot.config')

# Default configuration
DEFAULT_CONFIG = {
    "discord_token": "YOUR_DISCORD_BOT_TOKEN",
    "discord_channel_id": 123456789012345678,
    "urls": [],
    "check_interval": 30,
    "notification_cooldown": 3600,  # 1 hour in seconds
    "enable_cooldown": True
}

def load_config() -> Dict[str, Any]:
    """Load configuration from file with caching"""
    try:
        with open("ticket_alert_config.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except FileNotFoundError:
        logger.warning("Config file not found, creating default config")
        save_config(DEFAULT_CONFIG)
        return DEFAULT_CONFIG
    except Exception as e:
        logger.error(f"Error loading config: {e}")
        return {}

def save_config(config: Dict[str, Any]) -> bool:
    """Save configuration to file"""
    try:
        with open("ticket_alert_config.json", "w", encoding="utf-8") as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
        return True
    except Exception as e:
        logger.error(f"Error saving config: {e}")
        return False

def update_config(key: str, value: Any) -> bool:
    """Update a specific configuration value"""
    config = load_config()
    config[key] = value
    return save_config(config)

def get_config_value(key: str, default: Optional[Any] = None) -> Any:
    """Get a specific configuration value"""
    config = load_config()
    return config.get(key, default)
