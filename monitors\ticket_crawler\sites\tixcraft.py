"""
Tixcraft (拓元售票) 爬蟲實現
"""

import re
import asyncio
import logging
from typing import Dict, List, Optional, Any
from urllib.parse import urlparse, urljoin

from monitors.ticket_crawler.sites.base import (
    BaseCrawler, CrawlResult, EventInfo, TicketArea, TicketStatus
)

logger = logging.getLogger('monitors.ticket_crawler.tixcraft')

class TixcraftCrawler(BaseCrawler):
    """
    Tixcraft 票務網站爬蟲
    支持監控 tixcraft.com 的票券可用性
    """
    
    def __init__(self, **kwargs):
        super().__init__(
            site_name="Tixcraft",
            base_url="https://tixcraft.com",
            **kwargs
        )
        
        # Tixcraft 特定配置
        self.activity_base_url = "https://tixcraft.com/activity/detail/"
        self.ticket_base_url = "https://tixcraft.com/ticket/area/"
        
    def extract_event_id(self, event_url: str) -> Optional[str]:
        """
        從 Tixcraft URL 中提取活動ID
        
        支持的URL格式：
        - https://tixcraft.com/activity/detail/25_xalive
        - https://tixcraft.com/ticket/area/25_xalive/19055
        
        Args:
            event_url: 活動URL
            
        Returns:
            str: 活動ID（如 "25_xalive"）
        """
        patterns = [
            r'/activity/detail/([^/?]+)',
            r'/ticket/area/([^/?]+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, event_url)
            if match:
                return match.group(1)
        
        return None
    
    def is_valid_url(self, url: str) -> bool:
        """
        檢查是否為有效的 Tixcraft URL
        
        Args:
            url: 要檢查的URL
            
        Returns:
            bool: 是否有效
        """
        if not url:
            return False
            
        parsed = urlparse(url)
        if parsed.netloc != 'tixcraft.com':
            return False
            
        # 檢查路徑格式
        valid_patterns = [
            r'/activity/detail/[^/?]+',
            r'/ticket/area/[^/?]+',
        ]
        
        return any(re.search(pattern, parsed.path) for pattern in valid_patterns)
    
    def _convert_to_ticket_url(self, activity_url: str) -> str:
        """
        將活動詳情URL轉換為票券選擇URL
        
        Args:
            activity_url: 活動詳情URL
            
        Returns:
            str: 票券選擇URL
        """
        event_id = self.extract_event_id(activity_url)
        if not event_id:
            return activity_url
            
        # 如果已經是票券URL，直接返回
        if '/ticket/area/' in activity_url:
            return activity_url
            
        # 轉換為票券URL（需要session_id，這裡先用空字符串）
        return f"{self.ticket_base_url}{event_id}/"
    
    async def crawl_event(self, event_url: str) -> CrawlResult:
        """
        爬取 Tixcraft 活動的票券信息
        
        Args:
            event_url: 活動URL
            
        Returns:
            CrawlResult: 爬取結果
        """
        try:
            # 驗證URL
            if not self.is_valid_url(event_url):
                return CrawlResult(
                    success=False,
                    error_message=f"Invalid Tixcraft URL: {event_url}"
                )
            
            # 提取活動ID
            event_id = self.extract_event_id(event_url)
            if not event_id:
                return CrawlResult(
                    success=False,
                    error_message=f"Cannot extract event ID from URL: {event_url}"
                )
            
            self.logger.info(f"Starting to crawl Tixcraft event: {event_id}")
            
            # 首先嘗試獲取活動基本信息
            event_info = await self._get_event_info(event_url, event_id)
            
            # 然後獲取票券信息
            ticket_areas = await self._get_ticket_areas(event_url, event_id)
            
            return CrawlResult(
                success=True,
                event_info=event_info,
                ticket_areas=ticket_areas
            )
            
        except Exception as e:
            self.logger.error(f"Error crawling Tixcraft event {event_url}: {e}")
            return CrawlResult(
                success=False,
                error_message=str(e)
            )
    
    async def _get_event_info(self, event_url: str, event_id: str) -> EventInfo:
        """
        獲取活動基本信息
        
        Args:
            event_url: 活動URL
            event_id: 活動ID
            
        Returns:
            EventInfo: 活動信息
        """
        # 這裡需要實現實際的網頁爬取邏輯
        # 由於需要處理JavaScript，建議使用 Selenium 或 Playwright
        
        # 暫時返回基本信息
        return EventInfo(
            title=f"Tixcraft Event {event_id}",
            date="TBD",
            time="TBD", 
            venue="TBD",
            url=event_url,
            event_id=event_id
        )
    
    async def _get_ticket_areas(self, event_url: str, event_id: str) -> List[TicketArea]:
        """
        獲取票券區域信息
        
        Args:
            event_url: 活動URL
            event_id: 活動ID
            
        Returns:
            List[TicketArea]: 票券區域列表
        """
        # 這裡需要實現實際的票券信息爬取邏輯
        # 需要解析類似這樣的HTML結構：
        # <div>狂歡人生站區2800 33 seat(s) remaining</div>
        # <div>橙207區2400 Sold out</div>
        
        # 暫時返回示例數據
        return [
            TicketArea(
                name="狂歡人生站區",
                price=2800,
                status=TicketStatus.AVAILABLE,
                available_count=33
            ),
            TicketArea(
                name="橙207區",
                price=2400,
                status=TicketStatus.SOLD_OUT,
                available_count=0
            )
        ]
    
    def _parse_ticket_section(self, section_html: str) -> List[TicketArea]:
        """
        解析票券區域HTML
        
        Args:
            section_html: 區域HTML內容
            
        Returns:
            List[TicketArea]: 解析出的票券區域
        """
        ticket_areas = []
        
        # 解析價格區域標題（如 "2800區"）
        price_match = re.search(r'(\d+)區', section_html)
        base_price = int(price_match.group(1)) if price_match else 0
        
        # 解析各個具體區域
        # 匹配格式：區域名稱 + 狀態信息
        area_patterns = [
            r'([^<>\n]+?)\s+(\d+)\s+seat\(s\)\s+remaining',  # 有票
            r'([^<>\n]+?)\s+Sold\s+out',                      # 售完
        ]
        
        for pattern in area_patterns:
            matches = re.finditer(pattern, section_html, re.IGNORECASE)
            for match in matches:
                area_name = match.group(1).strip()
                
                if 'seat(s) remaining' in match.group(0):
                    # 有票
                    available_count = int(match.group(2))
                    status = TicketStatus.AVAILABLE
                else:
                    # 售完
                    available_count = 0
                    status = TicketStatus.SOLD_OUT
                
                ticket_areas.append(TicketArea(
                    name=area_name,
                    price=base_price,
                    status=status,
                    available_count=available_count
                ))
        
        return ticket_areas
    
    async def test_connection(self) -> bool:
        """
        測試與 Tixcraft 的連接
        
        Returns:
            bool: 連接是否正常
        """
        try:
            # 可以嘗試訪問首頁或其他公開頁面
            self.logger.info("Testing connection to Tixcraft...")
            # 這裡需要實現實際的連接測試
            return True
        except Exception as e:
            self.logger.error(f"Tixcraft connection test failed: {e}")
            return False
