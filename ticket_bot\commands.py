import discord
import datetime
import asyncio
import logging
from discord.ext import commands
from discord.ext.commands import has_permissions, Greedy
from typing import Dict, Any, List, Optional

from ticket_bot.config import load_config, save_config
from ticket_bot.api import get_session_info, get_ticket_info, check_single_event
from ticket_bot.utils import format_message, taiwan_tz

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('ticket_bot.commands')

# Global variables
bot_start_time = datetime.datetime.now()

class TicketCommands(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.command(name='check', help='手動檢查目前所有活動的票券狀態')
    async def check_tickets(self, ctx):
        """Manually check for available tickets"""
        # Send a typing indicator to show the bot is working
        async with ctx.typing():
            await ctx.send("🔍 正在檢查票券狀態，請稍候...")

            config = load_config()
            available_events = []
            tasks = []

            # Create tasks for all URLs
            for event_url in config.get("urls", []):
                tasks.append(check_single_event(event_url))

            # Run all tasks concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results
            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"Error checking event: {result}")
                    continue

                if result and result[0] and result[1] and sum(result[1].values()) > 0:
                    available_events.append(result)

            # Send results
            if available_events:
                await ctx.send(f"✅ 找到 {len(available_events)} 個有票券的活動:")
                for session_info, ticket_data in available_events:
                    embed = format_message(session_info, ticket_data)
                    await ctx.send(embed=embed)
            else:
                await ctx.send("❌ 目前沒有可用的票券。")

    @commands.command(name='list', help='列出所有監控中的活動')
    async def list_events(self, ctx):
        """List all events being monitored"""
        # Send a typing indicator to show the bot is working
        async with ctx.typing():
            config = load_config()

            if not config.get("urls", []):
                await ctx.send("❌ 目前沒有監控的活動。")
                return

            embed = discord.Embed(
                title="📋 監控中的活動",
                description=f"目前正在監控 {len(config.get('urls', []))} 個活動",
                color=discord.Color.blue(),
                timestamp=datetime.datetime.now(taiwan_tz)
            )

            # Create tasks for all URLs
            tasks = []
            for event_url in config.get("urls", []):
                tasks.append(check_single_event(event_url))

            # Run all tasks concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results
            for i, (event_url, result) in enumerate(zip(config.get("urls", []), results), 1):
                try:
                    if isinstance(result, Exception):
                        logger.error(f"Error checking event: {result}")
                        embed.add_field(
                            name=f"{i}. ⚠️ 錯誤",
                            value=f"URL: {event_url}\n錯誤: {str(result)}",
                            inline=False
                        )
                        continue

                    if result and result[0]:  # We have session info
                        session_info = result[0]
                        ticket_data = result[1] if result[1] else {}
                        total_tickets = sum(ticket_data.values())

                        # Determine status emoji
                        status_emoji = "❌" if total_tickets == 0 else "✅"

                        embed.add_field(
                            name=f"{i}. {status_emoji} {session_info['title']}",
                            value=f"📅 {session_info['date']} {session_info['time']}\n" +
                                  f"🎫 票數: {total_tickets}\n" +
                                  f"[活動連結]({session_info['url']})",
                            inline=False
                        )
                    else:
                        embed.add_field(
                            name=f"{i}. ❓ 未知活動",
                            value=f"URL: {event_url}\n無法獲取活動資訊",
                            inline=False
                        )
                except Exception as e:
                    logger.error(f"Error processing result for {event_url}: {e}")
                    embed.add_field(
                        name=f"{i}. ⚠️ 錯誤",
                        value=f"URL: {event_url}\n錯誤: {str(e)}",
                        inline=False
                    )

            await ctx.send(embed=embed)

    @commands.command(name='commands', help='顯示可用指令')
    async def commands_help(self, ctx):
        """Show help information"""
        embed = discord.Embed(
            title="🤖 票券監控機器人幫助",
            description="以下是可用的指令列表：",
            color=discord.Color.blue()
        )

        # 一般用戶指令
        embed.add_field(
            name="🔍 查詢指令",
            value=(
                "`!check` - 手動檢查目前所有活動的票券狀態\n"
                "`!list` - 列出所有監控中的活動\n"
                "`!search <關鍵字>` - 搜索活動\n"
                "`!stats` - 顯示票券統計信息\n"
                "`!commands` - 顯示此幫助訊息\n"
                "`!status` - 顯示機器人當前狀態"
            ),
            inline=False
        )

        # 管理員指令
        embed.add_field(
            name="⚙️ 管理員指令",
            value=(
                "`!add <url>` - 新增監控URL\n"
                "`!remove <index>` - 移除監控URL\n"
                "`!interval <seconds>` - 設定檢查間隔\n"
                "`!cooldown <minutes>` - 設定通知冷卻時間\n"
                "`!toggle_cooldown` - 開關冷卻功能\n"
                "`!notify_test [index]` - 測試通知\n"
                "`!clear <amount>` - 清除指定數量的訊息\n"
                "`!clear_bot <amount>` - 清除機器人訊息"
            ),
            inline=False
        )

        await ctx.send(embed=embed)

    @commands.command(name='status', help='顯示機器人當前狀態')
    async def show_status(self, ctx):
        """Show bot status"""
        uptime = datetime.datetime.now() - bot_start_time
        config = load_config()

        embed = discord.Embed(
            title="🤖 機器人狀態",
            color=discord.Color.blue(),
            timestamp=datetime.datetime.now(taiwan_tz)
        )

        # 基本狀態
        embed.add_field(
            name="⚙️ 基本信息",
            value=(
                f"機器人名稱: {self.bot.user.name}\n"
                f"運行時間: {uptime.days}天 {uptime.seconds//3600}小時 {(uptime.seconds//60)%60}分鐘\n"
                f"監控活動數: {len(config.get('urls', []))}"
            ),
            inline=False
        )

        # 設定信息
        embed.add_field(
            name="🔧 設定信息",
            value=(
                f"檢查間隔: {config.get('check_interval', 30)} 秒\n"
                f"通知冷卻: {config.get('notification_cooldown', 3600) // 60} 分鐘\n"
                f"冷卻功能: {'啟用' if config.get('enable_cooldown', True) else '停用'}"
            ),
            inline=False
        )

        # 系統信息
        embed.add_field(
            name="💻 系統信息",
            value=(
                f"Discord API 延遲: {round(self.bot.latency * 1000)} ms\n"
                f"版本: v1.0.0"
            ),
            inline=False
        )

        await ctx.send(embed=embed)

    @commands.command(name='add', help='新增監控URL')
    @has_permissions(administrator=True)
    async def add_url(self, ctx, url: str):
        """Add a new URL to monitor (admin only)"""
        if not url.startswith("https://ticketplus.com.tw/order/"):
            await ctx.send("❌ 無效的URL格式，請使用 ticketplus.com.tw 的訂票連結")
            return

        config = load_config()
        if url in config.get("urls", []):
            await ctx.send("⚠️ 此URL已在監控列表中")
            return

        # 檢查URL是否有效
        session_info = await get_session_info(url)
        if not session_info:
            await ctx.send("❌ 無法獲取活動信息，請確認URL是否正確")
            return

        # 確認URL有效後新增
        if "urls" not in config:
            config["urls"] = []
        config["urls"].append(url)

        save_config(config)

        # 創建成功崩帖
        embed = discord.Embed(
            title="✅ 新增監控成功",
            description=f"已新增活動: **{session_info['title']}**",
            color=discord.Color.green()
        )
        embed.add_field(
            name="📅 演出時間",
            value=f"{session_info['date']} {session_info['time']}",
            inline=False
        )
        embed.add_field(
            name="🔗 連結",
            value=f"[{url}]({url})",
            inline=False
        )

        if session_info.get('pic'):
            embed.set_thumbnail(url=session_info['pic'])

        await ctx.send(embed=embed)
        logger.info(f"Added new URL to monitor: {url}")

    @commands.command(name='remove', help='移除監控URL')
    @has_permissions(administrator=True)
    async def remove_url(self, ctx, index: int):
        """Remove a URL from monitoring (admin only)"""
        config = load_config()
        if "urls" not in config or not config["urls"]:
            await ctx.send("❌ 目前沒有監控的URL")
            return

        if index <= 0 or index > len(config["urls"]):
            await ctx.send(f"❌ 索引超出範圍，請使用1到{len(config['urls'])}之間的數字")
            return

        # 先獲取活動信息以顯示在回覆中
        removed_url = config["urls"][index-1]
        session_info = await get_session_info(removed_url)

        # 移除URL
        config["urls"].pop(index-1)
        save_config(config)

        # 創建成功崩帖
        embed = discord.Embed(
            title="✅ 移除監控成功",
            description=f"已移除活動: **{session_info['title'] if session_info else removed_url}**",
            color=discord.Color.red()
        )

        if session_info:
            embed.add_field(
                name="📅 演出時間",
                value=f"{session_info['date']} {session_info['time']}",
                inline=False
            )

        embed.add_field(
            name="🔗 連結",
            value=f"[{removed_url}]({removed_url})",
            inline=False
        )

        if session_info and session_info.get('pic'):
            embed.set_thumbnail(url=session_info['pic'])

        await ctx.send(embed=embed)
        logger.info(f"Removed URL from monitoring: {removed_url}")

    @commands.command(name='interval', help='設定檢查間隔（秒）')
    @has_permissions(administrator=True)
    async def set_interval(self, ctx, seconds: int):
        """Set check interval in seconds (admin only)"""
        if seconds < 1:
            await ctx.send("❌ 間隔時間不能小於1秒")
            return

        config = load_config()
        old_interval = config.get("check_interval", 30)
        config["check_interval"] = seconds

        save_config(config)

        # 更新任務間隔 - 這需要在主程式中處理
        # 這裡只發送通知

        embed = discord.Embed(
            title="✅ 設定檢查間隔成功",
            description=f"已將檢查間隔從 {old_interval} 秒變更為 {seconds} 秒",
            color=discord.Color.green()
        )
        await ctx.send(embed=embed)
        logger.info(f"Changed check interval from {old_interval}s to {seconds}s")

    @commands.command(name='cooldown', help='設定通知冷卻時間（分鐘）')
    @has_permissions(administrator=True)
    async def set_cooldown(self, ctx, minutes: int):
        """Set notification cooldown in minutes (admin only)"""
        if minutes < 0:
            await ctx.send("❌ 冷卻時間不能為負數")
            return

        config = load_config()
        old_cooldown = config.get("notification_cooldown", 3600) // 60
        config["notification_cooldown"] = minutes * 60  # 轉換為秒

        save_config(config)

        embed = discord.Embed(
            title="✅ 設定通知冷卻時間成功",
            description=f"已將通知冷卻時間從 {old_cooldown} 分鐘變更為 {minutes} 分鐘",
            color=discord.Color.green()
        )
        await ctx.send(embed=embed)
        logger.info(f"Changed notification cooldown from {old_cooldown}min to {minutes}min")

    @commands.command(name='toggle_cooldown', help='開關冷卻功能')
    @has_permissions(administrator=True)
    async def toggle_cooldown(self, ctx):
        """Toggle cooldown feature (admin only)"""
        config = load_config()
        current_status = config.get("enable_cooldown", True)
        config["enable_cooldown"] = not current_status

        save_config(config)

        new_status = "啟用" if config["enable_cooldown"] else "停用"
        embed = discord.Embed(
            title="✅ 切換冷卻功能成功",
            description=f"已{new_status}冷卻功能",
            color=discord.Color.green()
        )
        await ctx.send(embed=embed)
        logger.info(f"Toggled cooldown feature to: {new_status}")

    @commands.command(name='clear', help='清除訊息')
    @has_permissions(manage_messages=True)
    async def clear_messages(self, ctx, amount: int = 10):
        """Clear messages from the channel (admin only)"""
        if amount <= 0 or amount > 100:
            await ctx.send("❌ 請指定一個介於 1 到 100 之間的數字")
            return

        # 刪除指令訊息
        await ctx.message.delete()

        # 刪除指定數量的訊息
        deleted = await ctx.channel.purge(limit=amount)

        # 發送確認訊息，並在幾秒後刪除
        confirm_msg = await ctx.send(f"✅ 已刪除 {len(deleted)} 則訊息")
        await asyncio.sleep(3)
        await confirm_msg.delete()

        logger.info(f"Cleared {len(deleted)} messages in channel {ctx.channel.name}")

    @commands.command(name='clear_bot', help='清除機器人訊息')
    @has_permissions(manage_messages=True)
    async def clear_bot_messages(self, ctx, amount: int = 50):
        """Clear only bot messages from the channel (admin only)"""
        if amount <= 0 or amount > 100:
            await ctx.send("❌ 請指定一個介於 1 到 100 之間的數字")
            return

        # 刪除指令訊息
        await ctx.message.delete()

        # 檢查訊息是否來自機器人
        def is_bot(message):
            return message.author.bot

        # 刪除機器人訊息
        deleted = await ctx.channel.purge(limit=amount, check=is_bot)

        # 發送確認訊息，並在幾秒後刪除
        confirm_msg = await ctx.send(f"✅ 已刪除 {len(deleted)} 則機器人訊息")
        await asyncio.sleep(3)
        await confirm_msg.delete()

        logger.info(f"Cleared {len(deleted)} bot messages in channel {ctx.channel.name}")

    @commands.command(name='search', help='搜索活動')
    async def search_events(self, ctx, *, keyword: str):
        """Search for events by keyword"""
        if not keyword or len(keyword) < 2:
            await ctx.send("❌ 請輸入至少 2 個字的關鍵字")
            return

        async with ctx.typing():
            config = load_config()
            found_events = []
            tasks = []

            # 創建任務來檢查所有URL
            for event_url in config.get("urls", []):
                tasks.append(get_session_info(event_url))

            # 並行執行所有任務
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 處理結果
            for i, (event_url, result) in enumerate(zip(config.get("urls", []), results), 1):
                if isinstance(result, Exception) or not result:
                    continue

                # 檢查活動標題是否包含關鍵字
                if keyword.lower() in result['title'].lower():
                    found_events.append((i, event_url, result))

            # 發送結果
            if found_events:
                embed = discord.Embed(
                    title=f"🔍 搜索結果: '{keyword}'",
                    description=f"找到 {len(found_events)} 個符合的活動",
                    color=discord.Color.blue()
                )

                for idx, url, info in found_events:
                    embed.add_field(
                        name=f"{idx}. {info['title']}",
                        value=f"📅 {info['date']} {info['time']}\n" +
                              f"[活動連結]({info['url']})",
                        inline=False
                    )

                await ctx.send(embed=embed)
            else:
                await ctx.send(f"❌ 沒有找到包含 '{keyword}' 的活動")

    @commands.command(name='notify_test', help='測試通知')
    @has_permissions(administrator=True)
    async def test_notification(self, ctx, index: int = None):
        """Test notification for a specific event or all events"""
        config = load_config()

        if not config.get("urls", []):
            await ctx.send("❌ 目前沒有監控的活動")
            return

        async with ctx.typing():
            if index is not None:
                # 測試特定活動
                if index <= 0 or index > len(config.get("urls", [])):
                    await ctx.send(f"❌ 索引超出範圍，請使用1到{len(config.get('urls', []))}之間的數字")
                    return

                event_url = config["urls"][index-1]
                result = await check_single_event(event_url)

                if not result or not result[0] or not result[1]:
                    await ctx.send(f"❌ 無法獲取活動信息: {event_url}")
                    return

                session_info, ticket_data = result
                embed = format_message(session_info, ticket_data)

                # 添加測試標記
                embed.set_footer(text="測試通知 - 僅供測試用途")

                await ctx.send("✅ 測試通知：", embed=embed)
                logger.info(f"Sent test notification for {session_info['title']}")
            else:
                # 測試所有活動
                await ctx.send("✅ 正在為所有活動發送測試通知...")

                tasks = []
                for event_url in config.get("urls", []):
                    tasks.append(check_single_event(event_url))

                results = await asyncio.gather(*tasks, return_exceptions=True)

                success_count = 0
                for result in results:
                    if isinstance(result, Exception) or not result or not result[0] or not result[1]:
                        continue

                    session_info, ticket_data = result
                    embed = format_message(session_info, ticket_data)

                    # 添加測試標記
                    embed.set_footer(text="測試通知 - 僅供測試用途")

                    await ctx.send(embed=embed)
                    success_count += 1

                await ctx.send(f"✅ 已為 {success_count} 個活動發送測試通知")
                logger.info(f"Sent test notifications for {success_count} events")

    @commands.command(name='stats', help='顯示票券統計信息')
    async def show_stats(self, ctx):
        """Show ticket statistics"""
        async with ctx.typing():
            config = load_config()

            if not config.get("urls", []):
                await ctx.send("❌ 目前沒有監控的活動")
                return

            tasks = []
            for event_url in config.get("urls", []):
                tasks.append(check_single_event(event_url))

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 統計數據
            total_events = len(config.get("urls", []))
            events_with_tickets = 0
            total_tickets = 0
            max_tickets = 0
            max_tickets_event = None

            for result in results:
                if isinstance(result, Exception) or not result or not result[0] or not result[1]:
                    continue

                session_info, ticket_data = result
                event_tickets = sum(ticket_data.values())

                if event_tickets > 0:
                    events_with_tickets += 1
                    total_tickets += event_tickets

                    if event_tickets > max_tickets:
                        max_tickets = event_tickets
                        max_tickets_event = session_info

            # 創建統計崩帖
            embed = discord.Embed(
                title="📊 票券統計信息",
                color=discord.Color.blue(),
                timestamp=datetime.datetime.now(taiwan_tz)
            )

            embed.add_field(
                name="🎫 票券概況",
                value=(
                    f"監控活動數: {total_events}\n"
                    f"有票活動數: {events_with_tickets}\n"
                    f"總票數: {total_tickets}\n"
                    f"平均每活動票數: {total_tickets / total_events if total_events > 0 else 0:.1f}"
                ),
                inline=False
            )

            if max_tickets_event:
                embed.add_field(
                    name="💯 最多票活動",
                    value=(
                        f"活動: {max_tickets_event['title']}\n"
                        f"時間: {max_tickets_event['date']} {max_tickets_event['time']}\n"
                        f"票數: {max_tickets}\n"
                        f"[活動連結]({max_tickets_event['url']})"
                    ),
                    inline=False
                )

            await ctx.send(embed=embed)

def setup(bot):
    bot.add_cog(TicketCommands(bot))
