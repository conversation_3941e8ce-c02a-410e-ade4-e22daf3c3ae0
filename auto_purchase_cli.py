#!/usr/bin/env python3
"""
自動搶票命令行工具
"""
import os
import sys
import json
import argparse
import logging
from ticket_bot.auto_purchase import TicketPlusAutoPurchase

# 配置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("auto_purchase.log", encoding="utf-8"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('auto_purchase_cli')

def load_config():
    """加載配置文件"""
    config_path = os.path.join(os.path.dirname(__file__), "ticket_alert_config.json")
    try:
        with open(config_path, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加載配置文件失敗: {str(e)}")
        return None

def save_config(config):
    """保存配置文件"""
    config_path = os.path.join(os.path.dirname(__file__), "ticket_alert_config.json")
    try:
        with open(config_path, "w", encoding="utf-8") as f:
            json.dump(config, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        logger.error(f"保存配置文件失敗: {str(e)}")
        return False

def setup_args():
    """設置命令行參數"""
    parser = argparse.ArgumentParser(description="自動搶票命令行工具")
    
    # 基本參數
    parser.add_argument("--url", type=str, help="活動 URL")
    parser.add_argument("--username", type=str, help="TicketPlus 帳號")
    parser.add_argument("--password", type=str, help="TicketPlus 密碼")
    
    # 票券參數
    parser.add_argument("--areas", type=str, nargs="+", help="目標區域列表，按優先順序排序")
    parser.add_argument("--count", type=int, help="票數")
    
    # 其他參數
    parser.add_argument("--headless", action="store_true", help="是否使用無頭模式（不顯示瀏覽器界面）")
    parser.add_argument("--auto-confirm", action="store_true", help="是否自動確認購買（謹慎使用）")
    parser.add_argument("--save-config", action="store_true", help="是否保存配置到配置文件")
    
    return parser.parse_args()

def main():
    """主函數"""
    # 解析命令行參數
    args = setup_args()
    
    # 加載配置文件
    config = load_config()
    if config is None:
        logger.error("無法加載配置文件，程序退出")
        return 1
    
    # 獲取帳號密碼
    username = args.username or config.get("ticketplus_username", "")
    password = args.password or config.get("ticketplus_password", "")
    
    if not username or not password:
        logger.error("請提供 TicketPlus 帳號和密碼")
        return 1
    
    # 獲取活動 URL
    event_url = args.url
    if not event_url:
        # 使用配置文件中的第一個 URL
        if config.get("urls") and len(config.get("urls")) > 0:
            event_url = config["urls"][0]
        else:
            logger.error("請提供活動 URL")
            return 1
    
    # 獲取目標區域和票數
    auto_purchase_config = config.get("auto_purchase", {})
    target_areas = args.areas or auto_purchase_config.get("target_areas", [])
    ticket_count = args.count or auto_purchase_config.get("ticket_count", 2)
    
    if not target_areas:
        logger.error("請提供目標區域")
        return 1
    
    # 獲取其他參數
    headless = args.headless
    auto_confirm = args.auto_confirm or auto_purchase_config.get("auto_confirm", False)
    
    # 保存配置
    if args.save_config:
        config["ticketplus_username"] = username
        config["ticketplus_password"] = password
        config["auto_purchase"] = {
            "enabled": True,
            "target_areas": target_areas,
            "ticket_count": ticket_count,
            "auto_confirm": auto_confirm,
            "buyer_info": auto_purchase_config.get("buyer_info", {})
        }
        
        if not save_config(config):
            logger.warning("保存配置失敗，但程序將繼續執行")
    
    # 創建自動搶票實例
    auto_purchase = TicketPlusAutoPurchase(username, password, headless=headless)
    
    try:
        # 執行自動搶票
        logger.info(f"開始自動搶票: {event_url}")
        logger.info(f"目標區域: {', '.join(target_areas)}")
        logger.info(f"票數: {ticket_count}")
        
        result = auto_purchase.auto_purchase(
            event_url,
            target_areas,
            ticket_count,
            buyer_info=auto_purchase_config.get("buyer_info"),
            auto_confirm=auto_confirm
        )
        
        if result:
            logger.info("自動搶票成功")
            return 0
        else:
            logger.error("自動搶票失敗")
            return 1
    except KeyboardInterrupt:
        logger.info("用戶中斷程序")
        return 1
    except Exception as e:
        logger.error(f"發生錯誤: {str(e)}")
        return 1
    finally:
        # 等待用戶輸入，然後關閉瀏覽器
        input("按 Enter 鍵關閉瀏覽器...")
        auto_purchase.close()

if __name__ == "__main__":
    sys.exit(main())
