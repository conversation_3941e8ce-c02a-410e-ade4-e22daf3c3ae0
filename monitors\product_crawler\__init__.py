# Product Crawler Monitor Package
"""
商品庫存監控模組
透過網頁爬蟲監控各種電商網站的商品庫存狀態
"""

# from monitors.product_crawler.crawler import ProductCrawler
# from monitors.product_crawler.monitor import ProductCrawlerMonitor
# from monitors.product_crawler.sites import ShopeeeCrawler, PChomeCrawler, MomoCrawler

__version__ = "1.0.0"
__all__ = [
    # "ProductCrawler",
    # "ProductCrawlerMonitor",
    # "ShopeeeCrawler", 
    # "PChomeCrawler",
    # "MomoCrawler",
]

# TODO: 實現商品庫存監控功能
# 計劃支持的電商網站：
# - Shopee (蝦皮購物)
# - PChome 24h購物
# - momo購物網
# - Yahoo購物中心
# - 博客來
# - 誠品線上
# - 東森購物
# - 松果購物
