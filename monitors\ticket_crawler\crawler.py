"""
票券爬蟲主類別
統一管理各種票務網站的爬蟲
"""

import asyncio
import logging
from typing import Dict, List, Optional, Type, Any
from urllib.parse import urlparse

from monitors.ticket_crawler.sites.base import BaseCrawler, CrawlResult
from monitors.ticket_crawler.sites.tixcraft import TixcraftCrawler
from monitors.ticket_crawler.utils.anti_detection import AntiDetectionMixin

logger = logging.getLogger('monitors.ticket_crawler.crawler')

class TicketCrawler(AntiDetectionMixin):
    """
    票券爬蟲管理器
    統一管理和調度各種票務網站的爬蟲
    """
    
    def __init__(self, **kwargs):
        super().__init__()
        
        # 爬蟲配置
        self.config = kwargs
        self.max_concurrent = kwargs.get('max_concurrent', 5)
        self.default_timeout = kwargs.get('default_timeout', 30)
        
        # 註冊的爬蟲類別
        self._crawler_classes: Dict[str, Type[BaseCrawler]] = {}
        self._crawler_instances: Dict[str, BaseCrawler] = {}
        
        # 註冊內建爬蟲
        self._register_builtin_crawlers()
        
        logger.info("TicketCrawler initialized")
    
    def _register_builtin_crawlers(self):
        """註冊內建的爬蟲類別"""
        self.register_crawler('tixcraft', TixcraftCrawler)
        # 未來可以在這裡註冊更多爬蟲
        # self.register_crawler('kktix', KKTIXCrawler)
        # self.register_crawler('famiticket', FamiTicketCrawler)
    
    def register_crawler(self, site_name: str, crawler_class: Type[BaseCrawler]):
        """
        註冊新的爬蟲類別
        
        Args:
            site_name: 網站名稱
            crawler_class: 爬蟲類別
        """
        self._crawler_classes[site_name.lower()] = crawler_class
        logger.info(f"Registered crawler for {site_name}")
    
    def get_crawler(self, site_name: str) -> Optional[BaseCrawler]:
        """
        獲取指定網站的爬蟲實例
        
        Args:
            site_name: 網站名稱
            
        Returns:
            BaseCrawler: 爬蟲實例，如果不存在則返回None
        """
        site_name_lower = site_name.lower()
        
        # 如果已有實例，直接返回
        if site_name_lower in self._crawler_instances:
            return self._crawler_instances[site_name_lower]
        
        # 創建新實例
        if site_name_lower in self._crawler_classes:
            crawler_class = self._crawler_classes[site_name_lower]
            crawler_instance = crawler_class(**self.config)
            self._crawler_instances[site_name_lower] = crawler_instance
            return crawler_instance
        
        return None
    
    def detect_site(self, url: str) -> Optional[str]:
        """
        根據URL自動檢測網站類型
        
        Args:
            url: 要檢測的URL
            
        Returns:
            str: 網站名稱，如果無法檢測則返回None
        """
        if not url:
            return None
        
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        
        # 域名映射
        domain_mappings = {
            'tixcraft.com': 'tixcraft',
            'kktix.com': 'kktix',
            'famiticket.com.tw': 'famiticket',
            'ibon.com.tw': 'ibon',
        }
        
        for domain_pattern, site_name in domain_mappings.items():
            if domain_pattern in domain:
                return site_name
        
        # 如果域名映射失敗，嘗試用爬蟲的 is_valid_url 方法
        for site_name, crawler_class in self._crawler_classes.items():
            try:
                # 創建臨時實例進行檢測
                temp_crawler = crawler_class()
                if temp_crawler.is_valid_url(url):
                    return site_name
            except Exception as e:
                logger.debug(f"Error checking URL with {site_name} crawler: {e}")
                continue
        
        return None
    
    async def crawl_single_event(self, event_url: str, site_name: Optional[str] = None) -> CrawlResult:
        """
        爬取單個活動的票券信息
        
        Args:
            event_url: 活動URL
            site_name: 網站名稱，如果不提供則自動檢測
            
        Returns:
            CrawlResult: 爬取結果
        """
        try:
            # 自動檢測網站類型
            if not site_name:
                site_name = self.detect_site(event_url)
                if not site_name:
                    return CrawlResult(
                        success=False,
                        error_message=f"Cannot detect site type for URL: {event_url}"
                    )
            
            # 獲取對應的爬蟲
            crawler = self.get_crawler(site_name)
            if not crawler:
                return CrawlResult(
                    success=False,
                    error_message=f"No crawler available for site: {site_name}"
                )
            
            # 添加反爬蟲延遲
            await self.random_delay()
            
            # 執行爬取
            logger.info(f"Crawling {site_name} event: {event_url}")
            result = await crawler.crawl_event(event_url)
            
            logger.info(f"Crawl completed for {event_url}: success={result.success}")
            return result
            
        except Exception as e:
            logger.error(f"Error crawling event {event_url}: {e}")
            return CrawlResult(
                success=False,
                error_message=str(e)
            )
    
    async def crawl_multiple_events(self, event_urls: List[str], site_names: Optional[List[str]] = None) -> List[CrawlResult]:
        """
        並發爬取多個活動的票券信息
        
        Args:
            event_urls: 活動URL列表
            site_names: 對應的網站名稱列表，如果不提供則自動檢測
            
        Returns:
            List[CrawlResult]: 爬取結果列表
        """
        if not event_urls:
            return []
        
        # 準備任務
        tasks = []
        for i, event_url in enumerate(event_urls):
            site_name = site_names[i] if site_names and i < len(site_names) else None
            task = self.crawl_single_event(event_url, site_name)
            tasks.append(task)
        
        # 限制並發數量
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def limited_crawl(task):
            async with semaphore:
                return await task
        
        # 執行並發爬取
        logger.info(f"Starting concurrent crawl of {len(tasks)} events")
        results = await asyncio.gather(*[limited_crawl(task) for task in tasks], return_exceptions=True)
        
        # 處理異常結果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Exception in crawling task {i}: {result}")
                processed_results.append(CrawlResult(
                    success=False,
                    error_message=str(result)
                ))
            else:
                processed_results.append(result)
        
        return processed_results
    
    def get_supported_sites(self) -> List[str]:
        """
        獲取支持的網站列表
        
        Returns:
            List[str]: 支持的網站名稱列表
        """
        return list(self._crawler_classes.keys())
    
    def get_site_info(self, site_name: str) -> Optional[Dict[str, Any]]:
        """
        獲取指定網站的信息
        
        Args:
            site_name: 網站名稱
            
        Returns:
            Dict[str, Any]: 網站信息，如果不存在則返回None
        """
        crawler = self.get_crawler(site_name)
        if crawler:
            return crawler.get_site_info()
        return None
    
    async def test_all_crawlers(self) -> Dict[str, bool]:
        """
        測試所有爬蟲的連接狀態
        
        Returns:
            Dict[str, bool]: 網站名稱到連接狀態的映射
        """
        results = {}
        
        for site_name in self._crawler_classes.keys():
            try:
                crawler = self.get_crawler(site_name)
                if crawler:
                    results[site_name] = await crawler.test_connection()
                else:
                    results[site_name] = False
            except Exception as e:
                logger.error(f"Error testing {site_name} crawler: {e}")
                results[site_name] = False
        
        return results
    
    def get_crawler_stats(self) -> Dict[str, Any]:
        """
        獲取爬蟲統計信息
        
        Returns:
            Dict[str, Any]: 統計信息
        """
        return {
            "supported_sites": len(self._crawler_classes),
            "active_instances": len(self._crawler_instances),
            "sites": list(self._crawler_classes.keys()),
            "max_concurrent": self.max_concurrent,
            "request_count": self.request_count,
        }
