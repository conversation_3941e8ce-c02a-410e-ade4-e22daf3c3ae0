"""
選擇器工具
提供統一的元素選擇和數據提取方法
"""

import re
import logging
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass

logger = logging.getLogger('monitors.ticket_crawler.selectors')

@dataclass
class SelectorConfig:
    """選擇器配置"""
    css: Optional[str] = None           # CSS 選擇器
    xpath: Optional[str] = None         # XPath 選擇器
    regex: Optional[str] = None         # 正則表達式
    attribute: Optional[str] = None     # 要提取的屬性名
    text_only: bool = True              # 是否只提取文本
    multiple: bool = False              # 是否提取多個元素

class SelectorHelper:
    """
    選擇器輔助類
    提供統一的元素選擇和數據提取方法
    """
    
    @staticmethod
    def extract_text(element, selector_config: SelectorConfig) -> Union[str, List[str], None]:
        """
        從元素中提取文本
        
        Args:
            element: 網頁元素（BeautifulSoup 或 Selenium 元素）
            selector_config: 選擇器配置
            
        Returns:
            Union[str, List[str], None]: 提取的文本
        """
        try:
            # 根據元素類型選擇不同的處理方式
            if hasattr(element, 'find'):  # BeautifulSoup
                return SelectorHelper._extract_with_bs4(element, selector_config)
            elif hasattr(element, 'find_element'):  # Selenium WebDriver
                return SelectorHelper._extract_with_selenium(element, selector_config)
            else:
                logger.warning(f"Unsupported element type: {type(element)}")
                return None
                
        except Exception as e:
            logger.error(f"Error extracting text: {e}")
            return None
    
    @staticmethod
    def _extract_with_bs4(soup, selector_config: SelectorConfig) -> Union[str, List[str], None]:
        """
        使用 BeautifulSoup 提取數據
        
        Args:
            soup: BeautifulSoup 對象
            selector_config: 選擇器配置
            
        Returns:
            Union[str, List[str], None]: 提取的數據
        """
        elements = []
        
        # CSS 選擇器
        if selector_config.css:
            if selector_config.multiple:
                elements = soup.select(selector_config.css)
            else:
                element = soup.select_one(selector_config.css)
                elements = [element] if element else []
        
        # 提取文本或屬性
        results = []
        for element in elements:
            if not element:
                continue
                
            if selector_config.attribute:
                value = element.get(selector_config.attribute)
            elif selector_config.text_only:
                value = element.get_text(strip=True)
            else:
                value = str(element)
            
            if value:
                results.append(value)
        
        # 應用正則表達式
        if selector_config.regex and results:
            pattern = re.compile(selector_config.regex)
            filtered_results = []
            for result in results:
                matches = pattern.findall(str(result))
                if matches:
                    filtered_results.extend(matches)
            results = filtered_results
        
        # 返回結果
        if not results:
            return None
        elif selector_config.multiple:
            return results
        else:
            return results[0]
    
    @staticmethod
    def _extract_with_selenium(driver, selector_config: SelectorConfig) -> Union[str, List[str], None]:
        """
        使用 Selenium 提取數據
        
        Args:
            driver: Selenium WebDriver 對象
            selector_config: 選擇器配置
            
        Returns:
            Union[str, List[str], None]: 提取的數據
        """
        from selenium.webdriver.common.by import By
        from selenium.common.exceptions import NoSuchElementException
        
        elements = []
        
        try:
            # CSS 選擇器
            if selector_config.css:
                if selector_config.multiple:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector_config.css)
                else:
                    try:
                        element = driver.find_element(By.CSS_SELECTOR, selector_config.css)
                        elements = [element]
                    except NoSuchElementException:
                        elements = []
            
            # XPath 選擇器
            elif selector_config.xpath:
                if selector_config.multiple:
                    elements = driver.find_elements(By.XPATH, selector_config.xpath)
                else:
                    try:
                        element = driver.find_element(By.XPATH, selector_config.xpath)
                        elements = [element]
                    except NoSuchElementException:
                        elements = []
        
        except Exception as e:
            logger.error(f"Error finding elements: {e}")
            return None
        
        # 提取文本或屬性
        results = []
        for element in elements:
            try:
                if selector_config.attribute:
                    value = element.get_attribute(selector_config.attribute)
                elif selector_config.text_only:
                    value = element.text.strip()
                else:
                    value = element.get_attribute('outerHTML')
                
                if value:
                    results.append(value)
            except Exception as e:
                logger.warning(f"Error extracting from element: {e}")
                continue
        
        # 應用正則表達式
        if selector_config.regex and results:
            pattern = re.compile(selector_config.regex)
            filtered_results = []
            for result in results:
                matches = pattern.findall(str(result))
                if matches:
                    filtered_results.extend(matches)
            results = filtered_results
        
        # 返回結果
        if not results:
            return None
        elif selector_config.multiple:
            return results
        else:
            return results[0]
    
    @staticmethod
    def create_tixcraft_selectors() -> Dict[str, SelectorConfig]:
        """
        創建 Tixcraft 專用選擇器配置
        
        Returns:
            Dict[str, SelectorConfig]: 選擇器配置字典
        """
        return {
            # 活動標題
            "event_title": SelectorConfig(
                css="h1, .event-title, .activity-title",
                text_only=True
            ),
            
            # 活動日期時間
            "event_datetime": SelectorConfig(
                css=".event-date, .activity-date, .datetime",
                text_only=True
            ),
            
            # 活動場地
            "event_venue": SelectorConfig(
                css=".venue, .location, .event-venue",
                text_only=True
            ),
            
            # 票券區域列表
            "ticket_areas": SelectorConfig(
                css=".ticket-area, .area-item, .section-item",
                multiple=True,
                text_only=True
            ),
            
            # 票券價格
            "ticket_price": SelectorConfig(
                regex=r'(\d+)區',
                text_only=True
            ),
            
            # 剩餘票數
            "remaining_tickets": SelectorConfig(
                regex=r'(\d+)\s+seat\(s\)\s+remaining',
                text_only=True
            ),
            
            # 售完狀態
            "sold_out": SelectorConfig(
                regex=r'(sold\s+out|售完|已售完)',
                text_only=True
            ),
            
            # 區域名稱
            "area_name": SelectorConfig(
                regex=r'^([^0-9]+?)(?:\d+區|\d+\s+seat)',
                text_only=True
            ),
        }
    
    @staticmethod
    def create_kktix_selectors() -> Dict[str, SelectorConfig]:
        """
        創建 KKTIX 專用選擇器配置（預留）
        
        Returns:
            Dict[str, SelectorConfig]: 選擇器配置字典
        """
        return {
            "event_title": SelectorConfig(
                css=".event-title, h1",
                text_only=True
            ),
            # TODO: 添加更多 KKTIX 選擇器
        }
    
    @staticmethod
    def get_selectors_for_site(site_name: str) -> Dict[str, SelectorConfig]:
        """
        根據網站名稱獲取對應的選擇器配置
        
        Args:
            site_name: 網站名稱
            
        Returns:
            Dict[str, SelectorConfig]: 選擇器配置字典
        """
        site_name_lower = site_name.lower()
        
        if site_name_lower in ['tixcraft', 'tix']:
            return SelectorHelper.create_tixcraft_selectors()
        elif site_name_lower in ['kktix']:
            return SelectorHelper.create_kktix_selectors()
        else:
            logger.warning(f"No selectors defined for site: {site_name}")
            return {}
    
    @staticmethod
    def validate_selector_config(config: SelectorConfig) -> bool:
        """
        驗證選擇器配置是否有效
        
        Args:
            config: 選擇器配置
            
        Returns:
            bool: 是否有效
        """
        # 至少需要一個選擇器
        if not any([config.css, config.xpath, config.regex]):
            return False
        
        # 正則表達式驗證
        if config.regex:
            try:
                re.compile(config.regex)
            except re.error:
                return False
        
        return True
