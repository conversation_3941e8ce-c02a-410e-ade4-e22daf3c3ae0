import os
import logging
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
import sys

def setup_logging(
    log_dir="logs",
    log_file="ticket_bot.log",
    console_level=logging.INFO,
    file_level=logging.DEBUG,
    max_size_mb=10,
    backup_count=5
):
    """
    設置日誌系統，包括日誌輪轉功能
    
    參數:
        log_dir (str): 日誌文件目錄
        log_file (str): 日誌文件名稱
        console_level (int): 控制台日誌級別
        file_level (int): 文件日誌級別
        max_size_mb (int): 每個日誌文件的最大大小（MB）
        backup_count (int): 保留的舊日誌文件數量
    """
    # 創建日誌目錄（如果不存在）
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 完整的日誌文件路徑
    log_path = os.path.join(log_dir, log_file)
    
    # 創建根日誌記錄器
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)  # 捕獲所有級別的日誌
    
    # 清除任何現有的處理程序
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 創建控制台處理程序
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(console_level)
    console_format = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    console_handler.setFormatter(console_format)
    logger.addHandler(console_handler)
    
    # 創建文件處理程序（大小輪轉）
    # 當日誌文件達到指定大小時，它會被重命名，並創建一個新的日誌文件
    file_handler = RotatingFileHandler(
        log_path,
        maxBytes=max_size_mb * 1024 * 1024,  # 轉換為位元組
        backupCount=backup_count,
        encoding='utf-8'
    )
    file_handler.setLevel(file_level)
    file_format = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    file_handler.setFormatter(file_format)
    logger.addHandler(file_handler)
    
    # 記錄日誌系統已設置
    logger.info(f"Logging system initialized. Log file: {log_path}")
    logger.info(f"Log rotation settings: max_size={max_size_mb}MB, backups={backup_count}")
    
    return logger

def setup_timed_logging(
    log_dir="logs",
    log_file="ticket_bot.log",
    console_level=logging.INFO,
    file_level=logging.DEBUG,
    when='midnight',  # 'midnight', 'h' (hourly), 'd' (daily), 'w0'-'w6' (weekday)
    interval=1,  # 間隔數量
    backup_count=30  # 保留30天的日誌
):
    """
    設置基於時間的日誌輪轉
    
    參數:
        log_dir (str): 日誌文件目錄
        log_file (str): 日誌文件名稱
        console_level (int): 控制台日誌級別
        file_level (int): 文件日誌級別
        when (str): 輪轉時間單位
        interval (int): 輪轉時間間隔
        backup_count (int): 保留的舊日誌文件數量
    """
    # 創建日誌目錄（如果不存在）
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 完整的日誌文件路徑
    log_path = os.path.join(log_dir, log_file)
    
    # 創建根日誌記錄器
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)  # 捕獲所有級別的日誌
    
    # 清除任何現有的處理程序
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 創建控制台處理程序
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(console_level)
    console_format = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    console_handler.setFormatter(console_format)
    logger.addHandler(console_handler)
    
    # 創建文件處理程序（時間輪轉）
    # 當達到指定的時間間隔時，日誌文件會被重命名，並創建一個新的日誌文件
    file_handler = TimedRotatingFileHandler(
        log_path,
        when=when,
        interval=interval,
        backupCount=backup_count,
        encoding='utf-8'
    )
    file_handler.setLevel(file_level)
    file_format = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    file_handler.setFormatter(file_format)
    logger.addHandler(file_handler)
    
    # 記錄日誌系統已設置
    logger.info(f"Timed logging system initialized. Log file: {log_path}")
    logger.info(f"Log rotation settings: when={when}, interval={interval}, backups={backup_count}")
    
    return logger
