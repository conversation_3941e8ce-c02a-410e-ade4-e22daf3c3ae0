#!/usr/bin/env python3
"""
票券監控腳本
此腳本僅用於監控票券狀態，不包含購票功能
"""
import os
import sys
import json
import argparse
import logging
import asyncio
from ticket_bot.api import check_single_event, initialize_session, close_session

# 配置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("ticket_monitor.log", encoding="utf-8"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('ticket_monitor')

def load_config():
    """加載配置文件"""
    config_path = os.path.join(os.path.dirname(__file__), "ticket_alert_config.json")
    try:
        with open(config_path, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加載配置文件失敗: {str(e)}")
        return None

def setup_args():
    """設置命令行參數"""
    parser = argparse.ArgumentParser(description="票券監控腳本")
    
    # 基本參數
    parser.add_argument("--url", type=str, help="活動 URL")
    parser.add_argument("--check-once", action="store_true", help="只檢查一次，不持續監控")
    
    return parser.parse_args()

async def check_ticket_status(event_url):
    """檢查票券狀態"""
    try:
        result = await check_single_event(event_url)
        if result:
            session_info, ticket_data = result
            logger.info(f"活動: {session_info.get('title', 'Unknown')}")
            logger.info(f"狀態: {session_info.get('status', 'Unknown')}")
            
            if ticket_data:
                logger.info("票券狀態:")
                for area_name, available_count in ticket_data.items():
                    status = "有票" if available_count > 0 else "售完"
                    logger.info(f"  {area_name}: {available_count} 張 ({status})")
            else:
                logger.info("無法獲取票券詳細資訊")
        else:
            logger.error("無法獲取活動資訊")
    except Exception as e:
        logger.error(f"檢查票券狀態時發生錯誤: {str(e)}")

async def main():
    """主函數"""
    # 解析命令行參數
    args = setup_args()
    
    # 加載配置文件
    config = load_config()
    if config is None:
        logger.error("無法加載配置文件，程序退出")
        return 1
    
    # 獲取活動 URL
    event_url = args.url
    if not event_url:
        # 使用配置文件中的第一個 URL
        if config.get("urls") and len(config.get("urls")) > 0:
            event_url = config["urls"][0]
        else:
            logger.error("請提供活動 URL")
            return 1
    
    # 初始化 HTTP session
    await initialize_session()
    
    try:
        if args.check_once:
            # 只檢查一次
            logger.info(f"檢查票券狀態: {event_url}")
            await check_ticket_status(event_url)
        else:
            # 持續監控
            logger.info(f"開始監控票券狀態: {event_url}")
            logger.info("按 Ctrl+C 停止監控")
            
            while True:
                await check_ticket_status(event_url)
                await asyncio.sleep(30)  # 每30秒檢查一次
                
    except KeyboardInterrupt:
        logger.info("用戶中斷程序")
        return 0
    except Exception as e:
        logger.error(f"發生錯誤: {str(e)}")
        return 1
    finally:
        # 關閉 HTTP session
        await close_session()

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
