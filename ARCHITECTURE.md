# 專案架構說明

## 概述
本專案是一個多功能的 Discord 監控機器人，支持多種監控功能模組化設計，便於擴展和維護。

## 目錄結構
```
discord_bot_api/
├── README.md                    # 專案說明
├── ARCHITECTURE.md             # 架構說明（本文件）
├── requirements.txt            # Python 依賴
├── setup.py                   # 安裝配置
├── ticket_alert_config.json   # 主配置文件
├── ticket_bot_main.py         # Discord 機器人主程式
├── ticket_monitor_cli.py      # 命令行監控工具
│
├── ticket_bot/                # 核心機器人功能
│   ├── __init__.py
│   ├── config.py              # 配置管理
│   ├── utils.py               # 工具函數
│   ├── commands.py            # Discord 傳統指令
│   ├── slash_commands.py      # Discord 斜線指令
│   └── logging_utils.py       # 日誌工具
│
└── monitors/                  # 監控模組
    ├── __init__.py
    │
    ├── ticket_api/            # 票券 API 監控
    │   ├── __init__.py
    │   ├── api.py             # TicketPlus API 接口
    │   └── monitor.py         # API 監控器
    │
    ├── ticket_crawler/        # 票券爬蟲監控（計劃中）
    │   ├── __init__.py
    │   ├── README.md
    │   ├── crawler.py         # 爬蟲基礎類別
    │   ├── monitor.py         # 爬蟲監控器
    │   ├── sites/             # 各網站爬蟲
    │   ├── utils/             # 爬蟲工具
    │   └── config/            # 爬蟲配置
    │
    └── product_crawler/       # 商品庫存監控（計劃中）
        ├── __init__.py
        ├── README.md
        ├── crawler.py         # 商品爬蟲基礎類別
        ├── monitor.py         # 商品監控器
        ├── sites/             # 各電商網站爬蟲
        ├── utils/             # 商品監控工具
        ├── data/              # 數據存儲
        └── config/            # 商品監控配置
```

## 模組說明

### 1. 核心機器人 (ticket_bot/)
負責 Discord 機器人的基礎功能，包括：
- **config.py**: 配置文件管理
- **utils.py**: 通用工具函數（消息格式化、時區處理等）
- **commands.py**: Discord 傳統指令處理
- **slash_commands.py**: Discord 斜線指令處理
- **logging_utils.py**: 日誌系統配置

### 2. 票券 API 監控 (monitors/ticket_api/)
**狀態**: ✅ 已實現
**功能**: 透過 TicketPlus API 監控票券可用性
- **api.py**: API 請求處理、緩存、錯誤處理
- **monitor.py**: 監控邏輯、通知發送、冷卻管理

### 3. 票券爬蟲監控 (monitors/ticket_crawler/)
**狀態**: 📋 計劃中
**功能**: 透過網頁爬蟲監控各種票務網站
- 支持 KKTIX、FamiTicket、ibon 等票務平台
- 反爬蟲檢測對策
- 智能重試機制

### 4. 商品庫存監控 (monitors/product_crawler/)
**狀態**: 📋 計劃中
**功能**: 監控電商網站商品庫存和價格
- 支持 Shopee、PChome、momo 等電商平台
- 價格歷史追蹤
- 庫存變動通知

## 設計原則

### 1. 模組化設計
- 每個監控功能獨立成模組
- 統一的接口設計
- 便於新增和移除功能

### 2. 可擴展性
- 新監控類型可輕易加入
- 支持多種數據源
- 靈活的配置系統

### 3. 錯誤處理
- 完善的異常處理機制
- 自動重試和降級策略
- 詳細的日誌記錄

### 4. 性能優化
- 異步處理
- 請求緩存
- 連接池管理

## 配置系統

### 主配置文件 (ticket_alert_config.json)
```json
{
  "discord_token": "...",
  "discord_channel_id": 123456789,
  
  "ticket_api": {
    "enabled": true,
    "urls": ["..."],
    "check_interval": 30,
    "notification_cooldown": 3600
  },
  
  "ticket_crawler": {
    "enabled": false,
    "sites": {...}
  },
  
  "product_crawler": {
    "enabled": false,
    "sites": {...}
  }
}
```

### 模組特定配置
每個監控模組可以有自己的配置文件，位於各自的 `config/` 目錄下。

## 數據流程

### 1. 啟動流程
```
ticket_bot_main.py
├── 載入配置
├── 初始化 Discord 機器人
├── 註冊斜線指令
├── 啟動監控模組
└── 發送啟動通知
```

### 2. 監控流程
```
監控器 (Monitor)
├── 定時檢查
├── 調用 API/爬蟲
├── 比較數據變化
├── 發送 Discord 通知
└── 更新緩存數據
```

### 3. 指令處理流程
```
Discord 斜線指令
├── 權限檢查
├── 參數驗證
├── 調用對應功能
├── 格式化回應
└── 發送結果
```

## 開發指南

### 新增監控模組
1. 在 `monitors/` 下創建新目錄
2. 實現基礎類別繼承
3. 添加到主配置系統
4. 註冊相關 Discord 指令
5. 更新文檔

### 新增網站支持
1. 在對應模組的 `sites/` 目錄下新增文件
2. 繼承基礎爬蟲類別
3. 實現網站特定邏輯
4. 添加配置模板
5. 編寫測試

### 測試
- 單元測試：各模組獨立測試
- 整合測試：模組間協作測試
- 端到端測試：完整流程測試

## 部署建議

### 開發環境
```bash
python ticket_bot_main.py
```

### 生產環境
```bash
# 使用 systemd 服務
sudo systemctl start ticket-bot

# 或使用 Docker
docker run -d ticket-bot
```

### 監控和維護
- 日誌輪轉：自動管理日誌文件
- 健康檢查：定期檢查服務狀態
- 性能監控：追蹤資源使用情況

## 未來規劃

### Phase 1: 票券爬蟲監控
- 實現基礎爬蟲架構
- 支持主要票務網站
- 反爬蟲對策

### Phase 2: 商品庫存監控
- 實現商品監控功能
- 價格歷史追蹤
- 數據可視化

### Phase 3: 進階功能
- 機器學習預測
- 自動化操作
- 移動端支持

### Phase 4: 平台擴展
- 支持其他 IM 平台
- Web 管理界面
- API 服務化
