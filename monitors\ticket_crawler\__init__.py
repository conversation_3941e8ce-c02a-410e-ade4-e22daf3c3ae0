# Ticket Crawler Monitor Package
"""
票券爬蟲監控模組
透過網頁爬蟲監控各種票務網站的票券可用性
"""

# from monitors.ticket_crawler.crawler import TicketCrawler
# from monitors.ticket_crawler.monitor import TicketCrawlerMonitor
# from monitors.ticket_crawler.sites import TicketPlusCrawler, KKTIXCrawler, FamiTicketCrawler

__version__ = "1.0.0"
__all__ = [
    # "TicketCrawler",
    # "TicketCrawlerMonitor", 
    # "TicketPlusCrawler",
    # "KKTIXCrawler",
    # "FamiTicketCrawler",
]

# TODO: 實現票券爬蟲功能
# 計劃支持的票務網站：
# - TicketPlus (拓元售票)
# - KKTIX
# - FamiTicket (全家便利商店售票)
# - ibon (7-11 售票)
# - 年代售票
# - 寬宏售票
