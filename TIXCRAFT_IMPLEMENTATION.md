# Tixcraft 爬蟲實現說明

## 概述

本文檔說明了 Tixcraft (拓元售票) 爬蟲監控功能的實現，這是一個可擴展的票務網站監控架構的一部分。

## 架構設計

### 1. 模組化設計

```
monitors/ticket_crawler/
├── __init__.py                 # 模組初始化
├── crawler.py                  # 爬蟲管理器
├── monitor.py                  # 監控器
├── sites/                      # 各網站專用爬蟲
│   ├── __init__.py
│   ├── base.py                # 基礎爬蟲類別
│   └── tixcraft.py            # Tixcraft 專用爬蟲
└── utils/                      # 工具模組
    ├── __init__.py
    ├── anti_detection.py       # 反爬蟲檢測工具
    ├── selectors.py            # 選擇器工具
    └── parsers.py              # 數據解析工具
```

### 2. 核心類別

#### BaseCrawler (基礎爬蟲類別)
- 定義統一的爬蟲接口
- 提供共同的工具方法
- 標準化數據格式

#### TixcraftCrawler (Tixcraft 專用爬蟲)
- 繼承 BaseCrawler
- 實現 Tixcraft 特定的爬取邏輯
- 處理 Tixcraft URL 格式

#### TicketCrawler (爬蟲管理器)
- 統一管理多個網站爬蟲
- 自動檢測網站類型
- 支持並發爬取

#### TicketCrawlerMonitor (監控器)
- 定時執行爬蟲任務
- 發送 Discord 通知
- 管理冷卻機制

## 實現特色

### 1. 可擴展性
- **統一接口**: 所有網站爬蟲都實現相同的接口
- **自動註冊**: 新爬蟲可以輕易加入系統
- **配置驅動**: 通過配置文件控制爬蟲行為

### 2. 反爬蟲對策
- **User-Agent 輪換**: 隨機選擇不同的瀏覽器標識
- **請求延遲**: 隨機延遲避免被檢測
- **請求頭隨機化**: 模擬真實瀏覽器行為

### 3. 錯誤處理
- **重試機制**: 自動重試失敗的請求
- **降級策略**: 部分失敗時繼續其他任務
- **詳細日誌**: 便於調試和監控

### 4. 性能優化
- **並發處理**: 同時爬取多個URL
- **緩存機制**: 避免重複請求
- **連接池**: 復用HTTP連接

## 數據結構

### EventInfo (活動信息)
```python
@dataclass
class EventInfo:
    title: str                   # 活動標題
    date: str                    # 活動日期
    time: str                    # 活動時間
    venue: str                   # 場地
    url: str                     # 活動URL
    event_id: Optional[str]      # 活動ID
    image_url: Optional[str]     # 活動圖片
```

### TicketArea (票券區域)
```python
@dataclass
class TicketArea:
    name: str                    # 區域名稱
    price: int                   # 價格
    status: TicketStatus         # 狀態
    available_count: Optional[int] # 剩餘票數
    section_id: Optional[str]    # 區域ID
```

### CrawlResult (爬取結果)
```python
@dataclass
class CrawlResult:
    success: bool                # 是否成功
    event_info: Optional[EventInfo]     # 活動信息
    ticket_areas: List[TicketArea]      # 票券區域列表
    error_message: Optional[str]        # 錯誤信息
    timestamp: float                    # 爬取時間戳
```

## 配置說明

### 基本配置
```json
{
  "ticket_crawler": {
    "enabled": true,
    "check_interval": 300,
    "sites": {
      "tixcraft": {
        "enabled": true,
        "urls": [
          "https://tixcraft.com/ticket/area/25_xalive/19055"
        ],
        "max_retries": 3,
        "request_delay": 2.0
      }
    }
  }
}
```

### 反爬蟲配置
```json
{
  "anti_detection": {
    "random_delay": [1, 3],
    "user_agents_rotation": true,
    "proxy_rotation": false
  }
}
```

## 使用方式

### 1. 基本使用
```python
from monitors.ticket_crawler.crawler import TicketCrawler

# 創建爬蟲管理器
crawler = TicketCrawler()

# 爬取單個活動
result = await crawler.crawl_single_event(
    "https://tixcraft.com/ticket/area/25_xalive/19055"
)

# 檢查結果
if result.success:
    print(f"活動: {result.event_info.title}")
    for area in result.ticket_areas:
        print(f"  {area.name}: ${area.price}, 剩餘 {area.available_count} 張")
```

### 2. 並發爬取
```python
# 爬取多個活動
urls = [
    "https://tixcraft.com/ticket/area/25_xalive/19055",
    "https://tixcraft.com/activity/detail/25_xalive"
]

results = await crawler.crawl_multiple_events(urls)
for result in results:
    if result.success:
        print(f"成功爬取: {result.event_info.title}")
```

### 3. Discord 監控
```python
from monitors.ticket_crawler.monitor import TicketCrawlerMonitor

# 在 Discord 機器人中使用
monitor = TicketCrawlerMonitor(bot)
monitor.start_monitoring()
```

## 測試

### 運行測試
```bash
python test_tixcraft_crawler.py
```

### 測試內容
- URL 驗證和活動ID提取
- 網站類型自動檢測
- 單個和並發爬取
- 錯誤處理和無效URL處理
- 連接測試

## 擴展指南

### 新增網站支持

1. **創建新的爬蟲類別**
```python
# monitors/ticket_crawler/sites/newsite.py
class NewSiteCrawler(BaseCrawler):
    def __init__(self, **kwargs):
        super().__init__(
            site_name="NewSite",
            base_url="https://newsite.com",
            **kwargs
        )
    
    async def crawl_event(self, event_url: str) -> CrawlResult:
        # 實現具體的爬取邏輯
        pass
```

2. **註冊新爬蟲**
```python
# monitors/ticket_crawler/crawler.py
def _register_builtin_crawlers(self):
    self.register_crawler('tixcraft', TixcraftCrawler)
    self.register_crawler('newsite', NewSiteCrawler)  # 新增
```

3. **更新配置**
```json
{
  "ticket_crawler": {
    "sites": {
      "newsite": {
        "enabled": true,
        "urls": ["https://newsite.com/event/123"]
      }
    }
  }
}
```

## 注意事項

### 法律與道德
- 遵守網站的 robots.txt 和服務條款
- 合理控制請求頻率
- 僅用於個人監控用途
- 尊重網站的反爬蟲措施

### 技術限制
- 需要處理 JavaScript 渲染的內容
- 網站結構變更可能導致爬蟲失效
- 可能遇到驗證碼或登入要求
- IP 可能被封鎖的風險

### 維護建議
- 定期檢查爬蟲是否正常工作
- 監控錯誤日誌並及時修復
- 根據網站變更更新選擇器
- 適當調整請求頻率和延遲

## 未來規劃

### Phase 1: 完善 Tixcraft 支持
- [ ] 實現真實的網頁爬取邏輯
- [ ] 支持 Selenium/Playwright
- [ ] 處理 JavaScript 動態內容

### Phase 2: 新增網站支持
- [ ] KKTIX 爬蟲
- [ ] FamiTicket 爬蟲
- [ ] ibon 爬蟲

### Phase 3: 進階功能
- [ ] 驗證碼處理
- [ ] 代理輪換
- [ ] 機器學習反檢測

### Phase 4: 監控優化
- [ ] 實時監控儀表板
- [ ] 性能指標收集
- [ ] 自動故障恢復
