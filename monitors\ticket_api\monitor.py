import time
import asyncio
import logging
import discord
import datetime
from discord.ext import tasks
from typing import Dict, Any, Optional

from ticket_bot.config import load_config
from monitors.ticket_api.api import get_session_info, get_ticket_info, check_single_event
from ticket_bot.utils import format_message, taiwan_tz

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('monitors.ticket_api.monitor')

# Global variables
previous_data = {}
last_notification_time = {}
notification_cooldown = {}

class TicketAPIMonitor:
    def __init__(self, bot):
        self.bot = bot
        self.monitor_task = None

    def start_monitoring(self):
        """Start the monitoring task"""
        config = load_config()
        check_interval = config.get("check_interval", 30)

        # Create the task with the correct interval
        self.monitor_task = tasks.loop(seconds=check_interval)(self.monitor_tickets)

        # Start the task
        self.monitor_task.start()
        logger.info(f"Started ticket API monitoring task with interval of {check_interval} seconds")

    def update_interval(self, seconds: int):
        """Update the monitoring interval"""
        if self.monitor_task:
            self.monitor_task.change_interval(seconds=seconds)
            logger.info(f"Updated ticket API monitoring interval to {seconds} seconds")

    def stop_monitoring(self):
        """Stop the monitoring task"""
        if self.monitor_task:
            self.monitor_task.cancel()
            logger.info("Stopped ticket API monitoring task")

    async def send_startup_notification(self):
        """Send a startup notification to Discord"""
        config = load_config()
        channel = self.bot.get_channel(config["discord_channel_id"])
        
        if not channel:
            logger.error(f"Error: Could not find channel with ID {config['discord_channel_id']}")
            return

        embed = discord.Embed(
            title="🤖 票券監控機器人已啟動",
            description="票券 API 監控功能已開始運行",
            color=discord.Color.green(),
            timestamp=datetime.datetime.now(taiwan_tz)
        )
        
        embed.add_field(
            name="📊 監控狀態",
            value=f"正在監控 {len(config.get('urls', []))} 個活動",
            inline=True
        )
        
        embed.add_field(
            name="⏱️ 檢查間隔",
            value=f"{config.get('check_interval', 30)} 秒",
            inline=True
        )
        
        embed.add_field(
            name="🔔 通知設定",
            value=f"冷卻時間: {config.get('notification_cooldown', 3600) // 60} 分鐘",
            inline=True
        )

        try:
            await channel.send(embed=embed)
            logger.info("Sent startup notification to Discord")
        except Exception as e:
            logger.error(f"Failed to send startup notification: {e}")

    async def monitor_tickets(self):
        """Monitor ticket availability and send alerts when tickets are available"""
        global previous_data, last_notification_time, notification_cooldown

        config = load_config()  # Reload config each time to allow for runtime changes
        channel = self.bot.get_channel(config["discord_channel_id"])

        if not channel:
            logger.error(f"Error: Could not find channel with ID {config['discord_channel_id']}")
            return

        # Check if this is the first run after startup
        first_run = self.monitor_task.current_loop == 0

        # Create tasks for all URLs
        tasks = []
        urls = config.get("urls", [])

        # 清除不在配置文件中的 URL 的緩存數據
        for url in list(previous_data.keys()):
            if url not in urls:
                del previous_data[url]
                logger.info(f"Removed {url} from previous_data cache")

        for url in list(last_notification_time.keys()):
            if url not in urls:
                del last_notification_time[url]
                logger.info(f"Removed {url} from last_notification_time cache")

        for url in list(notification_cooldown.keys()):
            if url not in urls:
                del notification_cooldown[url]
                logger.info(f"Removed {url} from notification_cooldown cache")

        for event_url in urls:
            # Skip URLs in cooldown unless this is the first run
            if notification_cooldown.get(event_url, False) and not first_run:
                continue
            tasks.append(check_single_event(event_url))

        # Run all tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results
        for i, event_url in enumerate(config.get("urls", [])):
            if i >= len(results):
                continue

            result = results[i]

            # Skip URLs in cooldown unless this is the first run
            if notification_cooldown.get(event_url, False) and not first_run:
                continue

            if isinstance(result, Exception):
                logger.error(f"Error checking event {event_url}: {result}")
                continue

            if not result or not result[0] or not result[1]:
                logger.warning(f"No data returned for {event_url}")
                continue

            session_info, ticket_data = result
            current_total = sum(ticket_data.values())

            # Check if there are tickets available and if this is a change from before
            if current_total > 0:
                previous_total = previous_data.get(event_url, 0)
                
                # Send notification if:
                # 1. This is the first run (startup notification)
                # 2. Tickets became available (previous_total was 0, now > 0)
                # 3. More tickets became available (current_total > previous_total)
                should_notify = (
                    first_run or 
                    (previous_total == 0 and current_total > 0) or 
                    (current_total > previous_total and previous_total > 0)
                )

                if should_notify:
                    # Check cooldown
                    current_time = time.time()
                    last_notification = last_notification_time.get(event_url, 0)
                    cooldown_period = config.get("notification_cooldown", 3600)  # Default 1 hour
                    
                    if config.get("enable_cooldown", True) and not first_run:
                        if current_time - last_notification < cooldown_period:
                            logger.info(f"Skipping notification for {session_info['title']} due to cooldown")
                            continue

                    # Send notification
                    embed = format_message(session_info, ticket_data)
                    
                    if first_run:
                        embed.set_footer(text="啟動通知 - 目前票券狀態")
                    elif previous_total == 0:
                        embed.set_footer(text="🎉 票券現已開放！")
                    else:
                        embed.set_footer(text="📈 票券數量增加！")

                    try:
                        await channel.send(embed=embed)
                        last_notification_time[event_url] = current_time
                        
                        # Set cooldown if enabled
                        if config.get("enable_cooldown", True):
                            notification_cooldown[event_url] = True
                            
                            # Schedule cooldown reset
                            async def reset_cooldown():
                                await asyncio.sleep(cooldown_period)
                                notification_cooldown[event_url] = False
                                logger.info(f"Cooldown reset for {session_info['title']}")
                            
                            asyncio.create_task(reset_cooldown())
                        
                        logger.info(f"Sent notification for {session_info['title']} - {current_total} tickets available")
                    except Exception as e:
                        logger.error(f"Failed to send notification: {e}")

            # Update previous data
            previous_data[event_url] = current_total
