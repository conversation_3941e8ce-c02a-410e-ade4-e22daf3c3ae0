"""
基礎爬蟲類別
定義所有票務網站爬蟲的統一接口和共同功能
"""

import asyncio
import logging
import time
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum

# Configure logging
logger = logging.getLogger('monitors.ticket_crawler.base')

class TicketStatus(Enum):
    """票券狀態枚舉"""
    AVAILABLE = "available"      # 有票
    SOLD_OUT = "sold_out"       # 售完
    NOT_YET_OPEN = "not_open"   # 尚未開賣
    SALE_ENDED = "sale_ended"   # 銷售結束
    UNKNOWN = "unknown"         # 未知狀態

@dataclass
class TicketArea:
    """票券區域信息"""
    name: str                    # 區域名稱
    price: int                   # 價格
    status: TicketStatus         # 狀態
    available_count: Optional[int] = None  # 剩餘票數（如果可獲取）
    total_count: Optional[int] = None      # 總票數（如果可獲取）
    section_id: Optional[str] = None       # 區域ID
    additional_info: Optional[Dict[str, Any]] = None  # 額外信息

@dataclass
class EventInfo:
    """活動信息"""
    title: str                   # 活動標題
    date: str                    # 活動日期
    time: str                    # 活動時間
    venue: str                   # 場地
    url: str                     # 活動URL
    event_id: Optional[str] = None         # 活動ID
    image_url: Optional[str] = None        # 活動圖片
    description: Optional[str] = None      # 活動描述
    organizer: Optional[str] = None        # 主辦方

@dataclass
class CrawlResult:
    """爬蟲結果"""
    success: bool                # 是否成功
    event_info: Optional[EventInfo] = None     # 活動信息
    ticket_areas: List[TicketArea] = None      # 票券區域列表
    error_message: Optional[str] = None        # 錯誤信息
    timestamp: float = None                    # 爬取時間戳
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()
        if self.ticket_areas is None:
            self.ticket_areas = []

class BaseCrawler(ABC):
    """
    基礎爬蟲類別
    所有票務網站爬蟲都應該繼承此類別
    """
    
    def __init__(self, site_name: str, base_url: str, **kwargs):
        """
        初始化爬蟲
        
        Args:
            site_name: 網站名稱
            base_url: 網站基礎URL
            **kwargs: 其他配置參數
        """
        self.site_name = site_name
        self.base_url = base_url
        self.config = kwargs
        self.logger = logging.getLogger(f'monitors.ticket_crawler.{site_name.lower()}')
        
        # 爬蟲配置
        self.request_delay = kwargs.get('request_delay', 1.0)  # 請求間隔
        self.timeout = kwargs.get('timeout', 30)               # 超時時間
        self.max_retries = kwargs.get('max_retries', 3)        # 最大重試次數
        self.user_agent = kwargs.get('user_agent', self._get_default_user_agent())
        
    def _get_default_user_agent(self) -> str:
        """獲取默認 User-Agent"""
        return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    
    @abstractmethod
    async def crawl_event(self, event_url: str) -> CrawlResult:
        """
        爬取單個活動的票券信息
        
        Args:
            event_url: 活動URL
            
        Returns:
            CrawlResult: 爬取結果
        """
        pass
    
    @abstractmethod
    def extract_event_id(self, event_url: str) -> Optional[str]:
        """
        從URL中提取活動ID
        
        Args:
            event_url: 活動URL
            
        Returns:
            str: 活動ID，如果無法提取則返回None
        """
        pass
    
    @abstractmethod
    def is_valid_url(self, url: str) -> bool:
        """
        檢查URL是否為此網站的有效URL
        
        Args:
            url: 要檢查的URL
            
        Returns:
            bool: 是否有效
        """
        pass
    
    def get_site_info(self) -> Dict[str, str]:
        """
        獲取網站基本信息
        
        Returns:
            Dict: 包含網站名稱、基礎URL等信息
        """
        return {
            "site_name": self.site_name,
            "base_url": self.base_url,
            "crawler_type": self.__class__.__name__
        }
    
    async def test_connection(self) -> bool:
        """
        測試與網站的連接
        
        Returns:
            bool: 連接是否正常
        """
        try:
            # 子類可以重寫此方法來實現特定的連接測試
            return True
        except Exception as e:
            self.logger.error(f"Connection test failed: {e}")
            return False
    
    def _normalize_price(self, price_str: str) -> int:
        """
        標準化價格字符串為整數
        
        Args:
            price_str: 價格字符串（如 "NT$1200", "$1200", "1200"）
            
        Returns:
            int: 價格整數
        """
        import re
        # 移除所有非數字字符
        price_clean = re.sub(r'[^\d]', '', str(price_str))
        try:
            return int(price_clean) if price_clean else 0
        except ValueError:
            return 0
    
    def _normalize_count(self, count_str: str) -> Optional[int]:
        """
        標準化票數字符串為整數
        
        Args:
            count_str: 票數字符串（如 "33 seat(s) remaining", "50張"）
            
        Returns:
            int: 票數整數，如果無法解析則返回None
        """
        import re
        # 提取數字
        numbers = re.findall(r'\d+', str(count_str))
        try:
            return int(numbers[0]) if numbers else None
        except (ValueError, IndexError):
            return None
    
    def _detect_ticket_status(self, status_text: str, available_count: Optional[int] = None) -> TicketStatus:
        """
        根據狀態文本檢測票券狀態
        
        Args:
            status_text: 狀態文本
            available_count: 可用票數
            
        Returns:
            TicketStatus: 票券狀態
        """
        status_lower = status_text.lower()
        
        # 售完狀態
        if any(keyword in status_lower for keyword in ['sold out', '售完', '已售完', '無票', '搶購一空']):
            return TicketStatus.SOLD_OUT
        
        # 尚未開賣
        if any(keyword in status_lower for keyword in ['not yet open', '尚未開賣', '即將開賣', 'coming soon']):
            return TicketStatus.NOT_YET_OPEN
        
        # 銷售結束
        if any(keyword in status_lower for keyword in ['sale ended', '銷售結束', '已結束']):
            return TicketStatus.SALE_ENDED
        
        # 有票（根據票數判斷）
        if available_count is not None and available_count > 0:
            return TicketStatus.AVAILABLE
        elif available_count == 0:
            return TicketStatus.SOLD_OUT
        
        # 有票（根據關鍵字判斷）
        if any(keyword in status_lower for keyword in ['available', '有票', 'remaining', '剩餘', '可購買']):
            return TicketStatus.AVAILABLE
        
        return TicketStatus.UNKNOWN
    
    async def _delay_request(self):
        """在請求之間添加延遲"""
        if self.request_delay > 0:
            await asyncio.sleep(self.request_delay)
