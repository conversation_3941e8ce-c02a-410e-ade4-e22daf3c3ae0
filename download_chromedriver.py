#!/usr/bin/env python3
"""
下載 ChromeDriver 的腳本
"""
import os
import sys
import platform
import zipfile
import shutil
import urllib.request
import subprocess
import re

def get_chrome_version():
    """獲取 Chrome 版本"""
    system = platform.system()
    
    try:
        if system == "Windows":
            # 使用 reg query 獲取 Chrome 版本
            cmd = 'reg query "HKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon" /v version'
            output = subprocess.check_output(cmd, shell=True).decode('utf-8')
            match = re.search(r'version\s+REG_SZ\s+([\d.]+)', output)
            if match:
                return match.group(1)
        elif system == "Darwin":  # macOS
            # 使用 defaults read 獲取 Chrome 版本
            cmd = '/Applications/Google\\ Chrome.app/Contents/MacOS/Google\\ Chrome --version'
            output = subprocess.check_output(cmd, shell=True).decode('utf-8')
            match = re.search(r'Google Chrome ([\d.]+)', output)
            if match:
                return match.group(1)
        elif system == "Linux":
            # 使用 google-chrome --version 獲取 Chrome 版本
            cmd = 'google-chrome --version'
            output = subprocess.check_output(cmd, shell=True).decode('utf-8')
            match = re.search(r'Google Chrome ([\d.]+)', output)
            if match:
                return match.group(1)
    except Exception as e:
        print(f"無法獲取 Chrome 版本: {e}")
    
    return None

def download_chromedriver():
    """下載 ChromeDriver"""
    # 獲取 Chrome 版本
    chrome_version = get_chrome_version()
    if not chrome_version:
        print("無法獲取 Chrome 版本，請手動下載 ChromeDriver")
        print("下載地址: https://chromedriver.chromium.org/downloads")
        return False
    
    # 獲取主要版本號
    major_version = chrome_version.split('.')[0]
    print(f"Chrome 版本: {chrome_version} (主要版本號: {major_version})")
    
    # 獲取系統信息
    system = platform.system()
    if system == "Windows":
        platform_name = "win32"
        chromedriver_name = "chromedriver.exe"
    elif system == "Darwin":  # macOS
        if platform.machine() == "arm64":
            platform_name = "mac_arm64"
        else:
            platform_name = "mac64"
        chromedriver_name = "chromedriver"
    elif system == "Linux":
        if platform.machine() == "aarch64":
            platform_name = "linux64_arm"
        else:
            platform_name = "linux64"
        chromedriver_name = "chromedriver"
    else:
        print(f"不支持的系統: {system}")
        return False
    
    # 下載 ChromeDriver
    try:
        # 創建 webdriver 目錄
        os.makedirs("webdriver", exist_ok=True)
        
        # 下載 ChromeDriver
        download_url = f"https://chromedriver.storage.googleapis.com/LATEST_RELEASE_{major_version}"
        print(f"獲取最新版本的 ChromeDriver {major_version}...")
        with urllib.request.urlopen(download_url) as response:
            chromedriver_version = response.read().decode('utf-8').strip()
        
        print(f"下載 ChromeDriver {chromedriver_version}...")
        download_url = f"https://chromedriver.storage.googleapis.com/{chromedriver_version}/chromedriver_{platform_name}.zip"
        zip_file_path = os.path.join("webdriver", "chromedriver.zip")
        
        urllib.request.urlretrieve(download_url, zip_file_path)
        
        # 解壓 ChromeDriver
        print("解壓 ChromeDriver...")
        with zipfile.ZipFile(zip_file_path, 'r') as zip_ref:
            zip_ref.extractall("webdriver")
        
        # 刪除 zip 文件
        os.remove(zip_file_path)
        
        # 設置執行權限
        chromedriver_path = os.path.join("webdriver", chromedriver_name)
        if system != "Windows":
            os.chmod(chromedriver_path, 0o755)
        
        print(f"ChromeDriver {chromedriver_version} 下載完成: {chromedriver_path}")
        return True
    except Exception as e:
        print(f"下載 ChromeDriver 失敗: {e}")
        return False

if __name__ == "__main__":
    print("開始下載 ChromeDriver...")
    if download_chromedriver():
        print("ChromeDriver 下載成功")
    else:
        print("ChromeDriver 下載失敗")
        sys.exit(1)
