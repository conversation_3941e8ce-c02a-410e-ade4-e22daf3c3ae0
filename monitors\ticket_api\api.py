import re
import time
import asyncio
import aiohttp
import logging
import sys
import json
from typing import Dict, Optional, Any, List
from cachetools import TTL<PERSON>ache

# Check Python version for asyncio.timeout compatibility
PY_311_PLUS = sys.version_info >= (3, 11)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('monitors.ticket_api.api')

# Create a cache for API responses
session_cache = TTLCache(maxsize=100, ttl=60)  # Cache session info for 60 seconds
ticket_cache = TTLCache(maxsize=10, ttl=10)    # Cache ticket info for 10 seconds

# Create a connection pool for HTTP requests
session = None

async def initialize_session():
    """Initialize the global aiohttp session"""
    global session
    if session is None or session.closed:
        # Configure connection pooling and timeouts
        timeout = aiohttp.ClientTimeout(total=10, connect=3, sock_connect=3, sock_read=7)
        connector = aiohttp.TCPConnector(
            limit=20,                # Increase connection limit
            ttl_dns_cache=300,       # Cache DNS results for 5 minutes
            ssl=False,               # Disable SSL verification for better performance
            force_close=False,       # Keep connections open
            enable_cleanup_closed=True  # Clean up closed connections
        )
        session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            json_serialize=lambda x: json.dumps(x, ensure_ascii=False),  # Optimize JSON serialization
            raise_for_status=False   # Don't raise exceptions for non-200 status codes
        )
        logger.info("Initialized HTTP session with optimized connection pooling")
    return session

async def close_session():
    """Close the global aiohttp session"""
    global session
    if session and not session.closed:
        try:
            await session.close()
            # Wait for the session to fully close
            await asyncio.sleep(0.25)
            logger.info("Closed HTTP session")
        except Exception as e:
            logger.error(f"Error closing HTTP session: {e}")
    session = None

def extract_event_id(url: str) -> Optional[str]:
    """Extract event ID from URL"""
    match = re.search(r'/order/([a-f0-9]+)/', url)
    return match.group(1) if match else None

async def fetch_data(url: str) -> Optional[Dict[str, Any]]:
    """Asynchronous API request function with caching and retries"""
    # Use cache key based on URL
    cache_key = f"fetch_{url}"

    # Check if result is in cache
    if cache_key in session_cache:
        logger.debug(f"Cache hit for {url}")
        return session_cache[cache_key]

    # Initialize session if needed
    await initialize_session()

    # Try up to 3 times with exponential backoff
    for attempt in range(3):
        try:
            # Use a shorter timeout for faster failure detection
            timeout = aiohttp.ClientTimeout(total=5, connect=2, sock_connect=2, sock_read=3)

            # Add headers to improve request performance
            headers = {
                'Accept': 'application/json',
                'Connection': 'keep-alive',
                'User-Agent': 'TicketBot/1.0.0',
                'Accept-Encoding': 'gzip, deflate',  # Enable compression
            }

            async with session.get(url, headers=headers, timeout=timeout) as response:
                if response.status == 200:
                    # Use faster JSON parsing
                    result = await response.json(content_type=None)

                    # Store in cache with a small random TTL variation to prevent cache stampede
                    import random
                    ttl_variation = random.uniform(0.9, 1.1)  # ±10% variation
                    session_cache[cache_key] = result

                    return result
                elif response.status == 429:  # Rate limit
                    retry_after = int(response.headers.get('Retry-After', 1))
                    logger.warning(f"Rate limited on {url}, retrying after {retry_after}s")
                    await asyncio.sleep(retry_after)
                    continue
                else:
                    logger.warning(f"API request failed with status {response.status}: {url}")
                    return None
        except asyncio.TimeoutError:
            logger.warning(f"Timeout on attempt {attempt+1} for {url}")
            if attempt < 2:  # Don't sleep on the last attempt
                await asyncio.sleep(1 * (2 ** attempt))  # Exponential backoff: 1, 2, 4 seconds
        except Exception as e:
            logger.error(f"API request failed: {url}\nError: {e}")
            if attempt < 2:
                await asyncio.sleep(0.5 * (2 ** attempt))  # Shorter backoff for errors
            else:
                return None  # Give up after 3 attempts

    return None

async def get_session_info(event_url: str) -> Optional[Dict[str, Any]]:
    """Get event session information with caching"""
    # Check cache first
    if event_url in session_cache:
        return session_cache[event_url]

    event_id = extract_event_id(event_url)
    if not event_id:
        logger.warning(f"Failed to extract event ID from URL: {event_url}")
        return None

    sessions_url = f"https://apis.ticketplus.com.tw/config/api/v1/getS3?path=event/{event_id}/sessions.json"
    sessions_data = await fetch_data(sessions_url)

    if not sessions_data or "sessions" not in sessions_data:
        return None

    try:
        session = sessions_data["sessions"][0]
        result = {
            "title": session["name"],
            "date": session["date"],
            "time": session["time"],
            "url": f"https://ticketplus.com.tw/order/{event_id}/{session['sessionId']}",
            "pic": session.get("picBigArea", "")
        }

        # Cache the result
        session_cache[event_url] = result
        return result
    except (IndexError, KeyError) as e:
        logger.error(f"Error parsing session data for {event_url}: {e}")
        return None

async def get_ticket_info(event_url: str) -> Optional[Dict[str, int]]:
    """Get ticket information asynchronously with caching"""
    # Check cache first
    if event_url in ticket_cache:
        logger.debug(f"Ticket cache hit for {event_url}")
        return ticket_cache[event_url]

    try:
        # Extract event ID with caching
        event_id = extract_event_id(event_url)
        if not event_id:
            logger.warning(f"Failed to extract event ID for ticket info: {event_url}")
            return None

        # Generate API URLs
        ticket_areas_url = f"https://apis.ticketplus.com.tw/config/api/v1/getS3?path=event/{event_id}/ticketAreas.json"
        products_url = f"https://apis.ticketplus.com.tw/config/api/v1/getS3?path=event/{event_id}/products.json"

        # Fetch data concurrently with a timeout
        try:
            # Set a timeout for the entire operation
            if PY_311_PLUS:
                # Python 3.11+ version using asyncio.timeout
                async with asyncio.timeout(5.0):
                    # Fetch data concurrently
                    ticket_areas_task = asyncio.create_task(fetch_data(ticket_areas_url))
                    products_task = asyncio.create_task(fetch_data(products_url))

                    # Wait for both requests to complete
                    ticket_areas_data, products_data = await asyncio.gather(ticket_areas_task, products_task)
            else:
                # Python 3.10 and earlier version using asyncio.wait_for
                # Create tasks
                ticket_areas_task = asyncio.create_task(fetch_data(ticket_areas_url))
                products_task = asyncio.create_task(fetch_data(products_url))

                # Wait for both tasks with a timeout
                await asyncio.wait_for(asyncio.gather(ticket_areas_task, products_task), timeout=5.0)

                # Get results
                ticket_areas_data = ticket_areas_task.result()
                products_data = products_task.result()
        except asyncio.TimeoutError:
            logger.warning(f"Timeout fetching ticket data for {event_url}")
            return None

        if not all([ticket_areas_data, products_data]):
            logger.warning(f"Failed to fetch ticket areas or products data for {event_url}")
            return None

        # Process ticket areas - use dict comprehension for better performance
        ticket_areas = ticket_areas_data.get("ticketAreas", [])
        ticket_area_dict = {area["ticketAreaId"]: area["name"] for area in ticket_areas}

        # Extract IDs - use list comprehensions for better performance
        ticket_area_ids = [area["ticketAreaId"] for area in ticket_areas]
        product_ids = [product["productId"] for product in products_data.get("products", [])]

        if not ticket_area_ids or not product_ids:
            logger.warning(f"No ticket area IDs or product IDs found for {event_url}")
            return None

        # Get ticket info
        timestamp = int(time.time() * 1000)

        # Optimize URL construction
        ticket_area_ids_str = ",".join(ticket_area_ids)
        product_ids_str = ",".join(product_ids)
        ticket_info_url = f"https://apis.ticketplus.com.tw/config/api/v1/get?ticketAreaId={ticket_area_ids_str}&productId={product_ids_str}&reload=false&_={timestamp}"

        # Fetch ticket info with a shorter timeout
        try:
            if PY_311_PLUS:
                # Python 3.11+ version
                async with asyncio.timeout(3.0):
                    ticket_info_data = await fetch_data(ticket_info_url)
            else:
                # Python 3.10 and earlier version
                ticket_info_data = await asyncio.wait_for(fetch_data(ticket_info_url), timeout=3.0)
        except asyncio.TimeoutError:
            logger.warning(f"Timeout fetching ticket info for {event_url}")
            return None

        if not ticket_info_data or "result" not in ticket_info_data:
            logger.warning(f"Failed to fetch ticket info data for {event_url}")
            return None

        # Process ticket counts - optimize with better error handling
        ticket_counts = {}
        for area in ticket_info_data["result"].get("ticketArea", []):
            area_id = area.get("id")
            if not area_id:
                continue

            try:
                count = int(area.get("count", 0) or 0)
                area_name = ticket_area_dict.get(area_id, area_id)
                ticket_counts[area_name] = count
            except (ValueError, TypeError) as e:
                logger.error(f"Error processing ticket count for area {area_id}: {e}")
                ticket_counts[ticket_area_dict.get(area_id, area_id)] = 0

        # Cache the result
        ticket_cache[event_url] = ticket_counts
        return ticket_counts

    except Exception as e:
        logger.error(f"Error getting ticket info for {event_url}: {e}")
        return None

async def check_single_event(event_url: str) -> Optional[tuple]:
    """Check a single event for available tickets"""
    try:
        # Set a timeout for the entire operation
        if PY_311_PLUS:
            # Python 3.11+ version
            async with asyncio.timeout(8.0):  # 8 second timeout for the entire operation
                # Fetch session info and ticket data concurrently
                session_info_task = asyncio.create_task(get_session_info(event_url))
                ticket_data_task = asyncio.create_task(get_ticket_info(event_url))

                # Wait for both tasks to complete
                session_info, ticket_data = await asyncio.gather(session_info_task, ticket_data_task)
        else:
            # Python 3.10 and earlier version
            # Create tasks
            session_info_task = asyncio.create_task(get_session_info(event_url))
            ticket_data_task = asyncio.create_task(get_ticket_info(event_url))

            # Wait with timeout
            await asyncio.wait_for(asyncio.gather(session_info_task, ticket_data_task), timeout=8.0)

            # Get results
            session_info = session_info_task.result()
            ticket_data = ticket_data_task.result()

        if not session_info or not ticket_data:
            return None

        return (session_info, ticket_data)
    except asyncio.TimeoutError:
        logger.warning(f"Timeout checking event {event_url}")
        return None
    except Exception as e:
        logger.error(f"Error checking tickets for {event_url}: {e}")
        return None
