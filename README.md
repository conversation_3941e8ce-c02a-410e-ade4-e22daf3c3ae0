# 多功能監控機器人

這是一個多功能的 Discord 監控機器人，支持多種監控功能。

## 功能模組

### 1. 票券 API 監控 (TicketPlus)
- 透過 API 監控 TicketPlus 網站上的票券可用性
- 當票券可用時發送 Discord 通知
- 支持多個活動同時監控

### 2. 票券爬蟲監控 (即將推出)
- 透過網頁爬蟲監控各種票務網站
- 支持更多票務平台
- 繞過 API 限制

### 3. 商品庫存監控 (即將推出)
- 監控電商網站商品庫存狀態
- 支持多種電商平台
- 商品補貨通知

## 共同功能
- 使用斜線指令（Slash Commands）進行操作
- 統一的 Discord 通知系統
- 靈活的配置管理

## 安裝

### 1. 安裝依賴

```bash
pip install -r requirements.txt
```

### 2. 配置

創建 `ticket_alert_config.json` 文件，包含以下配置：

```json
{
    "discord_token": "YOUR_DISCORD_BOT_TOKEN",
    "discord_channel_id": 123456789012345678,

    "urls": [
        "https://ticketplus.com.tw/order/XXXXXXXX/YYYYYYYY"
    ],
    "check_interval": 0.5,
    "notification_cooldown": 3600,
    "enable_cooldown": true,

    "ticket_crawler": {
        "enabled": true,
        "check_interval": 300,
        "sites": {
            "tixcraft": {
                "enabled": true,
                "urls": [
                    "https://tixcraft.com/ticket/area/25_xalive/19055"
                ],
                "max_retries": 3,
                "request_delay": 2.0
            }
        },
        "anti_detection": {
            "random_delay": [1, 3],
            "user_agents_rotation": true,
            "proxy_rotation": false
        }
    }
}
```

## 使用方法

### 使用 Discord 機器人

#### 啟動機器人

```bash
python ticket_bot_main.py
```

#### 使用斜線指令

機器人支持以下斜線指令：

##### 一般用戶指令

- `/check` - 手動檢查目前所有活動的票券狀態
- `/list` - 列出所有監控中的活動
- `/search <關鍵字>` - 搜索活動
- `/stats` - 顯示票券統計信息
- `/help` - 顯示幫助信息
- `/status` - 顯示機器人當前狀態

##### 管理員指令

- `/add <url>` - 新增監控URL
- `/remove <index>` - 移除監控URL
- `/interval <seconds>` - 設定檢查間隔
- `/cooldown <minutes>` - 設定通知冷卻時間
- `/toggle_cooldown` - 開關冷卻功能
- `/notify_test [index]` - 測試通知
- `/clear <amount>` - 清除指定數量的訊息
- `/clear_bot <amount>` - 清除機器人訊息

### 使用命令行監控工具

您也可以使用命令行工具來直接監控票券狀態：

#### TicketPlus API 監控
```bash
# 檢查一次票券狀態
python ticket_monitor_cli.py --check-once

# 持續監控票券狀態（每30秒檢查一次）
python ticket_monitor_cli.py

# 監控指定的活動URL
python ticket_monitor_cli.py --url "https://ticketplus.com.tw/order/xxx/yyy" --check-once
```

#### Tixcraft 爬蟲測試
```bash
# 測試 Tixcraft 爬蟲功能
python test_tixcraft_crawler.py
```



## 部署

### 使用 systemd 部署（Linux）

1. 創建 systemd 服務文件：

```bash
sudo nano /etc/systemd/system/ticket-bot.service
```

2. 添加以下內容：

```ini
[Unit]
Description=Ticket Alert Discord Bot
After=network.target

[Service]
Type=simple
User=your_username
WorkingDirectory=/path/to/discord_bot_api
ExecStart=/usr/bin/python3 ticket_bot_main.py
Restart=on-failure
RestartSec=5
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=ticket_bot

[Install]
WantedBy=multi-user.target
```

3. 啟用並啟動服務：

```bash
sudo systemctl daemon-reload
sudo systemctl enable ticket-bot
sudo systemctl start ticket-bot
```

## 日誌輪轉

機器人使用日誌輪轉功能，每天午夜會自動輪轉日誌文件，並保留 30 天的日誌。日誌文件存儲在 `logs` 目錄中。

## 注意事項

- 請確保您的 Discord 機器人有足夠的權限。

## 許可證

MIT
