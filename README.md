# 票券監控機器人

這是一個用於監控 TicketPlus 票券可用性的 Discord 機器人。

## 功能

- 監控 TicketPlus 網站上的票券可用性
- 當票券可用時發送 Discord 通知
- 支持多個活動同時監控
- 支持自動搶票功能（可選）
- 使用斜線指令（Slash Commands）進行操作

## 安裝

### 1. 安裝依賴

```bash
# 安裝基本依賴
pip install -r requirements.txt

# 如果要使用自動搶票功能，還需要安裝以下依賴
pip install selenium webdriver-manager
```

### 2. 下載 ChromeDriver（如果要使用自動搶票功能）

如果您想使用自動搶票功能，需要安裝 ChromeDriver。您可以使用以下方法之一：

#### 方法 1：使用 webdriver_manager 自動下載（推薦）

安裝 webdriver_manager 後，程序會自動下載和管理 ChromeDriver：

```bash
pip install webdriver-manager
```

#### 方法 2：使用提供的腳本下載

```bash
python download_chromedriver.py
```

#### 方法 3：手動下載

1. 訪問 [ChromeDriver 下載頁面](https://chromedriver.chromium.org/downloads)
2. 下載與您的 Chrome 版本相符的 ChromeDriver
3. 解壓並將 ChromeDriver 放在以下位置之一：
   - 當前目錄
   - `webdriver` 子目錄
   - 系統路徑

### 3. 配置

創建 `ticket_alert_config.json` 文件，包含以下配置：

```json
{
    "discord_token": "YOUR_DISCORD_BOT_TOKEN",
    "discord_channel_id": 123456789012345678,
    "urls": [
        "https://ticketplus.com.tw/order/XXXXXXXX/YYYYYYYY"
    ],
    "check_interval": 0.5,
    "notification_cooldown": 3600,
    "enable_cooldown": true,
    "ticketplus_username": "",
    "ticketplus_password": "",
    "auto_purchase": {
        "enabled": false,
        "target_areas": ["搖滾區", "看台區"],
        "ticket_count": 2,
        "auto_confirm": false,
        "buyer_info": {
            "name": "",
            "phone": "",
            "email": ""
        }
    }
}
```

## 使用方法

### 啟動機器人

```bash
python ticket_bot_main.py
```

### 使用斜線指令

機器人支持以下斜線指令：

#### 一般用戶指令

- `/check` - 手動檢查目前所有活動的票券狀態
- `/list` - 列出所有監控中的活動
- `/search <關鍵字>` - 搜索活動
- `/stats` - 顯示票券統計信息
- `/help` - 顯示幫助信息
- `/status` - 顯示機器人當前狀態

#### 管理員指令

- `/add <url>` - 新增監控URL
- `/remove <index>` - 移除監控URL
- `/interval <seconds>` - 設定檢查間隔
- `/cooldown <minutes>` - 設定通知冷卻時間
- `/toggle_cooldown` - 開關冷卻功能
- `/notify_test [index]` - 測試通知
- `/clear <amount>` - 清除指定數量的訊息
- `/clear_bot <amount>` - 清除機器人訊息

#### 自動搶票指令

- `/auto_purchase [event_index] [areas] [count] [auto_confirm]` - 自動搶票
- `/test_purchase_flow [event_index] [areas] [count]` - 測試購票流程

## 自動搶票功能

請參考 [AUTO_PURCHASE_README.md](AUTO_PURCHASE_README.md) 了解自動搶票功能的詳細使用說明。

## 部署

### 使用 systemd 部署（Linux）

1. 創建 systemd 服務文件：

```bash
sudo nano /etc/systemd/system/ticket-bot.service
```

2. 添加以下內容：

```ini
[Unit]
Description=Ticket Alert Discord Bot
After=network.target

[Service]
Type=simple
User=your_username
WorkingDirectory=/path/to/discord_bot_api
ExecStart=/usr/bin/python3 ticket_bot_main.py
Restart=on-failure
RestartSec=5
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=ticket_bot

[Install]
WantedBy=multi-user.target
```

3. 啟用並啟動服務：

```bash
sudo systemctl daemon-reload
sudo systemctl enable ticket-bot
sudo systemctl start ticket-bot
```

## 日誌輪轉

機器人使用日誌輪轉功能，每天午夜會自動輪轉日誌文件，並保留 30 天的日誌。日誌文件存儲在 `logs` 目錄中。

## 注意事項

- 自動搶票功能可能違反網站的服務條款，請謹慎使用。
- 請確保您的 Discord 機器人有足夠的權限。
- 如果您使用自動搶票功能，請確保您的 Chrome 瀏覽器版本與 ChromeDriver 版本相符。

## 許可證

MIT
