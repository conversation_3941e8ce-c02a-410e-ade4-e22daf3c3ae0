# 商品庫存監控模組

## 概述
此模組透過網頁爬蟲技術監控各種電商網站的商品庫存狀態，當商品補貨或價格變動時發送通知。

## 計劃支持的電商網站

### 已計劃支持
- [ ] **Shopee (蝦皮購物)** - 東南亞最大電商平台
- [ ] **PChome 24h購物** - 台灣知名電商平台
- [ ] **momo購物網** - 台灣大型購物網站
- [ ] **Yahoo購物中心** - Yahoo 電商平台
- [ ] **博客來** - 圖書、文具、生活用品
- [ ] **誠品線上** - 書籍、文創商品
- [ ] **東森購物** - 電視購物網站
- [ ] **松果購物** - 台灣本土電商

### 監控功能
- 📦 **庫存狀態監控** - 缺貨、有貨、預購狀態
- 💰 **價格變動追蹤** - 降價、漲價、促銷活動
- 🏷️ **商品信息更新** - 標題、描述、規格變更
- ⭐ **評價數量監控** - 新評價、評分變化
- 🚚 **配送信息追蹤** - 配送方式、運費變動

### 技術特色
- 🔄 **智能庫存檢測** - 多種庫存狀態識別策略
- 💲 **價格歷史記錄** - 價格趨勢分析和圖表
- 🎯 **精確商品定位** - 支持 SKU、變體、規格監控
- ⚡ **高效批量監控** - 異步處理大量商品
- 🛡️ **反爬蟲對策** - 模擬真實用戶行為
- 📊 **數據可視化** - 價格趨勢圖、庫存統計

## 目錄結構
```
monitors/product_crawler/
├── __init__.py              # 模組初始化
├── README.md               # 說明文件
├── crawler.py              # 商品爬蟲基礎類別
├── monitor.py              # 監控管理器
├── sites/                  # 各網站專用爬蟲
│   ├── __init__.py
│   ├── base.py            # 基礎商品爬蟲類別
│   ├── shopee.py          # Shopee 爬蟲
│   ├── pchome.py          # PChome 爬蟲
│   ├── momo.py            # momo 爬蟲
│   ├── yahoo.py           # Yahoo 爬蟲
│   ├── books.py           # 博客來爬蟲
│   ├── eslite.py          # 誠品線上爬蟲
│   ├── etmall.py          # 東森購物爬蟲
│   └── pcone.py           # 松果購物爬蟲
├── utils/                  # 工具函數
│   ├── __init__.py
│   ├── price_parser.py    # 價格解析工具
│   ├── stock_detector.py  # 庫存檢測工具
│   ├── product_parser.py  # 商品信息解析
│   └── image_handler.py   # 商品圖片處理
├── data/                   # 數據存儲
│   ├── __init__.py
│   ├── models.py          # 數據模型
│   ├── storage.py         # 數據存儲接口
│   └── history.py         # 歷史數據管理
└── config/                 # 配置文件
    ├── __init__.py
    ├── sites.json         # 網站配置
    └── products.json      # 商品配置模板
```

## 使用方式

### 基本配置
```json
{
  "product_crawler": {
    "enabled": true,
    "sites": {
      "shopee": {
        "enabled": true,
        "products": [
          {
            "url": "https://shopee.tw/product/123456/789012",
            "name": "商品名稱",
            "target_price": 1000,
            "notify_on_stock": true,
            "notify_on_price_drop": true
          }
        ],
        "check_interval": 300
      },
      "pchome": {
        "enabled": true,
        "products": [],
        "check_interval": 600
      }
    },
    "notifications": {
      "stock_available": true,
      "price_drop": true,
      "price_target_reached": true,
      "new_reviews": false
    },
    "data_retention": {
      "price_history_days": 90,
      "stock_history_days": 30
    }
  }
}
```

### Discord 指令
```
/product_add <site> <url>           # 新增商品監控
/product_remove <site> <product_id> # 移除商品監控
/product_list [site]                # 列出監控商品
/product_status <product_id>        # 查看商品狀態
/product_price_history <product_id> # 查看價格歷史
/product_set_target <product_id> <price> # 設定目標價格
```

## 監控類型

### 1. 庫存監控
- **缺貨 → 有貨**: 商品重新上架通知
- **有貨 → 缺貨**: 商品售完通知（可選）
- **預購開放**: 預購商品開放通知
- **限量商品**: 限量商品庫存變化

### 2. 價格監控
- **降價通知**: 商品價格下降
- **目標價格**: 達到設定的目標價格
- **促銷活動**: 折扣、優惠券、滿減活動
- **價格異常**: 價格大幅波動警告

### 3. 商品信息監控
- **商品更新**: 標題、描述、規格變更
- **新增變體**: 新顏色、尺寸、規格
- **評價更新**: 新評價、評分變化
- **賣家變更**: 賣家信息變動

## 數據存儲

### 價格歷史
```json
{
  "product_id": "shopee_123456_789012",
  "price_history": [
    {
      "timestamp": "2024-01-01T00:00:00Z",
      "price": 1200,
      "original_price": 1500,
      "discount": 20,
      "currency": "TWD"
    }
  ]
}
```

### 庫存記錄
```json
{
  "product_id": "shopee_123456_789012", 
  "stock_history": [
    {
      "timestamp": "2024-01-01T00:00:00Z",
      "in_stock": true,
      "stock_count": 50,
      "stock_status": "available"
    }
  ]
}
```

## 開發計劃

### Phase 1: 基礎架構
- [ ] 實現商品爬蟲基礎類別
- [ ] 建立數據模型和存儲
- [ ] 設計監控管理器

### Phase 2: 主要網站支持
- [ ] 實現 Shopee 爬蟲
- [ ] 實現 PChome 爬蟲
- [ ] 實現 momo 爬蟲

### Phase 3: 進階功能
- [ ] 價格歷史分析
- [ ] 庫存預測算法
- [ ] 促銷活動檢測

### Phase 4: 數據可視化
- [ ] 價格趨勢圖表
- [ ] 庫存統計儀表板
- [ ] 監控報告生成

## 注意事項

⚠️ **法律與道德考量**
- 遵守各電商網站的使用條款
- 合理控制爬蟲頻率，避免影響網站正常運營
- 僅用於個人購物決策，不得用於商業競爭
- 尊重商家和平台的權益

⚠️ **技術挑戰**
- 電商網站通常有較強的反爬蟲機制
- 商品頁面結構經常變更
- 價格和庫存信息可能通過 JavaScript 動態加載
- 需要處理登入、驗證碼等複雜情況

⚠️ **使用限制**
- 某些商品可能需要登入才能查看價格
- 部分網站限制訪問頻率
- 價格可能因地區、會員等級而不同
- 庫存信息可能不夠準確或有延遲
